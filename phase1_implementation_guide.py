#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第一阶段实施指南 - 变量提取增强
具体的代码修改示例和实施步骤
"""

import re
import ast
from typing import Dict, Optional, Any
from datetime import datetime

# ============================================================================
# 第一步：创建简单的变量提取器
# 新建文件：core/utils/variable_extractor.py
# ============================================================================

class SimpleVariableExtractor:
    """
    简单变量提取器
    使用正则表达式提取常见的数据分析变量模式
    """
    
    def __init__(self):
        # 定义常见的变量模式和对应的描述
        self.variable_patterns = {
            # 分组汇总模式
            r'(\w+)\s*=\s*df\.groupby\([^)]+\)\[[^]]+\]\.sum\(\)': '分组汇总数据',
            r'(\w+)\s*=\s*df\.groupby\([^)]+\)\[[^]]+\]\.mean\(\)': '分组平均值数据',
            r'(\w+)\s*=\s*df\.groupby\([^)]+\)\[[^]]+\]\.count\(\)': '分组计数数据',
            
            # 排序模式
            r'(\w+)\s*=\s*\w+\.sort_values\([^)]*ascending=False[^)]*\)': '降序排列数据',
            r'(\w+)\s*=\s*\w+\.sort_values\([^)]*ascending=True[^)]*\)': '升序排列数据',
            
            # 索引访问模式
            r'(\w+)\s*=\s*\w+\.index\[0\]': '排名第一的项目名称',
            r'(\w+)\s*=\s*\w+\.iloc\[0\]': '最高数值',
            r'(\w+)\s*=\s*\w+\.iloc\[-1\]': '最低数值',
            
            # 聚合计算模式
            r'(\w+)\s*=\s*\w+\.sum\(\)': '总计数值',
            r'(\w+)\s*=\s*\w+\.mean\(\)': '平均值',
            r'(\w+)\s*=\s*\w+\.max\(\)': '最大值',
            r'(\w+)\s*=\s*\w+\.min\(\)': '最小值',
            
            # 筛选模式
            r'(\w+)\s*=\s*df\[df\[[^]]+\]\s*==\s*[^]]+\]': '筛选后的数据',
            
            # 计算模式
            r'(\w+)\s*=\s*\([^)]+\s*/\s*[^)]+\s*\*\s*100\)': '百分比计算结果',
        }
    
    def extract_key_variables(self, code: str) -> Dict[str, str]:
        """
        提取关键变量的简化描述
        
        Args:
            code: Python代码字符串
            
        Returns:
            变量名到描述的映射
        """
        variables = {}
        
        # 清理代码，移除注释和空行
        cleaned_code = self._clean_code(code)
        
        # 应用所有模式
        for pattern, description in self.variable_patterns.items():
            matches = re.findall(pattern, cleaned_code, re.MULTILINE | re.DOTALL)
            for var_name in matches:
                if isinstance(var_name, tuple):
                    var_name = var_name[0]  # 如果是元组，取第一个元素
                
                # 避免重复，优先保留更具体的描述
                if var_name not in variables:
                    variables[var_name] = description
        
        # 后处理：根据变量名推断更具体的含义
        variables = self._enhance_variable_descriptions(variables, cleaned_code)
        
        return variables
    
    def _clean_code(self, code: str) -> str:
        """清理代码，移除注释和多余空白"""
        lines = []
        for line in code.split('\n'):
            # 移除注释
            if '#' in line:
                line = line[:line.index('#')]
            # 保留非空行
            if line.strip():
                lines.append(line)
        return '\n'.join(lines)
    
    def _enhance_variable_descriptions(self, variables: Dict[str, str], code: str) -> Dict[str, str]:
        """根据变量名和上下文增强描述"""
        enhanced = {}
        
        for var_name, base_description in variables.items():
            # 根据变量名模式增强描述
            if 'product' in var_name.lower():
                if '分组汇总' in base_description:
                    enhanced[var_name] = '各产品的汇总数据'
                elif '排名第一' in base_description:
                    enhanced[var_name] = '销售最佳的产品名称'
                else:
                    enhanced[var_name] = f'产品相关的{base_description}'
            
            elif 'region' in var_name.lower():
                if '分组汇总' in base_description:
                    enhanced[var_name] = '各地区的汇总数据'
                elif '排名第一' in base_description:
                    enhanced[var_name] = '表现最佳的地区名称'
                else:
                    enhanced[var_name] = f'地区相关的{base_description}'
            
            elif 'sales' in var_name.lower():
                enhanced[var_name] = f'销售额相关的{base_description}'
            
            elif 'best' in var_name.lower() or 'max' in var_name.lower():
                enhanced[var_name] = f'最佳/最高的{base_description}'
            
            elif 'total' in var_name.lower():
                enhanced[var_name] = f'总计的{base_description}'
            
            else:
                enhanced[var_name] = base_description
        
        return enhanced

# ============================================================================
# 第二步：修改现有的 ContextManager
# 修改文件：core/utils/context_manager.py
# ============================================================================

def get_enhanced_update_reference_tracker_code():
    """
    返回增强的 _update_reference_tracker 方法代码
    这个方法需要替换现有的方法
    """
    return '''
    def _update_reference_tracker(self, code: Optional[str], execution_result: Optional[Dict]):
        """
        更新引用跟踪器（增强版）
        
        Args:
            code: 生成的代码
            execution_result: 执行结果
        """
        if not code:
            return
        
        context_state = st.session_state.context_manager
        tracker = context_state['reference_tracker']
        
        code_lower = code.lower()
        
        # 现有的跟踪逻辑保持不变
        # 跟踪图表生成
        if any(keyword in code_lower for keyword in ['chart', 'plot', 'figure', 'graph']):
            tracker['last_chart'] = {
                'code': code,
                'timestamp': datetime.now().isoformat(),
                'success': execution_result.get('success', False) if execution_result else False
            }
        
        # 跟踪表格生成
        if any(keyword in code_lower for keyword in ['dataframe', 'table', 'st.dataframe', 'st.table']):
            tracker['last_table'] = {
                'code': code,
                'timestamp': datetime.now().isoformat(),
                'success': execution_result.get('success', False) if execution_result else False
            }
        
        # 跟踪分析代码
        if any(keyword in code_lower for keyword in ['groupby', 'sum', 'mean', 'count', 'describe']):
            tracker['last_analysis'] = {
                'code': code,
                'timestamp': datetime.now().isoformat(),
                'success': execution_result.get('success', False) if execution_result else False
            }
        
        # 新增：提取变量信息（仅在代码执行成功时）
        if code and execution_result and execution_result.get('success'):
            try:
                # 导入变量提取器（需要在文件顶部添加导入）
                from .variable_extractor import SimpleVariableExtractor
                
                extractor = SimpleVariableExtractor()
                extracted_vars = extractor.extract_key_variables(code)
                
                # 更新提取的变量信息
                if extracted_vars:
                    if 'extracted_variables' not in tracker:
                        tracker['extracted_variables'] = {}
                    
                    # 添加时间戳和来源信息
                    for var_name, description in extracted_vars.items():
                        tracker['extracted_variables'][var_name] = {
                            'description': description,
                            'timestamp': datetime.now().isoformat(),
                            'source_code_length': len(code),
                            'round_number': context_state['round_counter']
                        }
                    
                    if self.logger:
                        self.logger.info(f"提取到 {len(extracted_vars)} 个变量: {list(extracted_vars.keys())}")
                
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"变量提取失败: {str(e)}")
                # 失败时不影响现有功能
                pass
    '''

# ============================================================================
# 第三步：修改 session state 初始化
# 修改文件：core/utils/context_manager.py 中的 _init_context_state 方法
# ============================================================================

def get_enhanced_init_context_state_code():
    """
    返回增强的 _init_context_state 方法代码
    """
    return '''
    def _init_context_state(self):
        """初始化上下文相关的session state（增强版）"""
        if 'context_manager' not in st.session_state:
            st.session_state.context_manager = {
                'conversation_rounds': [],  # 对话轮次列表
                'current_summary': None,  # 当前摘要
                'round_counter': 0,  # 轮次计数器
                'last_summary_round': 0,  # 上次摘要的轮次
                'topic_changed': False,  # 主题是否发生变化
                'reference_tracker': {  # 引用跟踪器
                    'last_chart': None,
                    'last_table': None,
                    'last_analysis': None,
                    'variables': {},  # 现有的变量跟踪
                    'extracted_variables': {}  # 新增：结构化变量信息
                }
            }
    '''

# ============================================================================
# 第四步：测试代码
# ============================================================================

def test_variable_extraction():
    """测试变量提取功能"""
    print("🧪 测试变量提取功能")
    print("=" * 50)
    
    # 测试代码示例
    test_code = '''
import pandas as pd
import streamlit as st

# 计算各产品销售额
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)

# 显示条形图
st.subheader("📊 各产品销售额分析")
st.bar_chart(product_sales)

# 找出最佳产品
best_product = product_sales.index[0]
best_amount = product_sales.iloc[0]

# 计算总销售额
total_sales = product_sales.sum()

# 计算占比
best_ratio = (best_amount / total_sales * 100)
    '''
    
    # 创建提取器并测试
    extractor = SimpleVariableExtractor()
    variables = extractor.extract_key_variables(test_code)
    
    print("提取的变量:")
    for var_name, description in variables.items():
        print(f"  • {var_name}: {description}")
    
    print(f"\n总共提取到 {len(variables)} 个变量")
    
    # 验证关键变量是否被正确识别
    expected_vars = ['product_sales', 'best_product', 'best_amount', 'total_sales']
    found_vars = list(variables.keys())
    
    print(f"\n验证结果:")
    for expected in expected_vars:
        if expected in found_vars:
            print(f"  ✅ {expected} - 已识别")
        else:
            print(f"  ❌ {expected} - 未识别")

# ============================================================================
# 第五步：部署检查清单
# ============================================================================

def deployment_checklist():
    """部署检查清单"""
    checklist = [
        "1. 创建 core/utils/variable_extractor.py 文件",
        "2. 在 core/utils/context_manager.py 顶部添加导入：from .variable_extractor import SimpleVariableExtractor",
        "3. 替换 _init_context_state 方法",
        "4. 替换 _update_reference_tracker 方法",
        "5. 运行测试验证功能正常",
        "6. 检查现有功能是否受影响",
        "7. 观察日志输出确认变量提取工作正常"
    ]
    
    print("📋 第一阶段部署检查清单:")
    print("=" * 50)
    for item in checklist:
        print(f"  □ {item}")
    
    print(f"\n⚠️ 重要提醒:")
    print("  • 修改前请备份原文件")
    print("  • 建议在测试环境先验证")
    print("  • 如有问题可随时回退到原版本")

if __name__ == "__main__":
    # 运行测试
    test_variable_extraction()
    print("\n")
    deployment_checklist()
    
    print(f"\n🎉 第一阶段实施指南完成!")
    print("这个阶段的改动非常安全，只是在现有功能基础上增加变量提取，不会影响现有的提示词构建逻辑。")
