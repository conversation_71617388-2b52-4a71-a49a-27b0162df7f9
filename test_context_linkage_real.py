#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实环境下的上下文联动机制测试
"""

import sys
from pathlib import Path
import pandas as pd
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_real_context_linkage():
    """测试真实环境下的上下文联动"""
    print("🔗 真实环境上下文联动测试")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from core.integrations.streamlit_integration import StreamlitLLMIntegration
        from core.utils.context_manager import ContextManager
        import streamlit as st
        
        # 模拟streamlit session state
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self._state = {}
                
                def __getattr__(self, key):
                    return self._state.get(key)
                
                def __setattr__(self, key, value):
                    if key.startswith('_'):
                        super().__setattr__(key, value)
                    else:
                        self._state[key] = value
                
                def __contains__(self, key):
                    return key in self._state
                
                def get(self, key, default=None):
                    return self._state.get(key, default)
            
            st.session_state = MockSessionState()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04'],
            '产品名称': ['笔记本电脑', '台式电脑', '平板电脑', '笔记本电脑'],
            '销售额': [8500, 6200, 3200, 7800],
            '销量': [5, 3, 8, 4],
            '地区': ['北京', '上海', '广州', '深圳'],
            '销售员': ['张三', '李四', '王五', '赵六']
        })
        
        # 初始化集成实例
        integration = StreamlitLLMIntegration()
        
        # 加载测试数据
        st.session_state.current_data = test_data
        st.session_state.data_name = "sales_test.csv"
        
        print("✅ 环境初始化完成")
        print(f"📊 测试数据: {test_data.shape}")
        
        # 第一轮对话测试
        print("\n🎯 第一轮对话测试")
        print("-" * 40)
        
        first_instruction = "分析各产品的销售额，找出表现最好的产品"
        
        # 模拟第一轮对话（不使用真实LLM调用）
        first_code = '''
# 分析各产品销售额
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
st.subheader("📊 各产品销售额分析")
st.bar_chart(product_sales)
st.dataframe(product_sales.reset_index())

# 找出最佳产品
best_product = product_sales.index[0]
best_amount = product_sales.iloc[0]
st.success(f"销售额最高的产品: {best_product} (¥{best_amount:,})")
'''
        
        # 添加第一轮对话到上下文
        should_summarize_1 = integration.add_conversation_round(
            user_message=first_instruction,
            assistant_message="我已经分析了各产品的销售额，笔记本电脑表现最佳。",
            code=first_code,
            execution_result={"success": True}
        )
        
        print(f"✅ 第一轮对话已添加")
        print(f"📊 是否需要摘要: {should_summarize_1}")
        
        # 获取上下文统计
        stats_1 = integration.get_context_stats()
        print(f"📈 当前统计: {stats_1['total_rounds']} 轮对话")
        
        # 第二轮对话测试
        print("\n🎯 第二轮对话测试")
        print("-" * 40)
        
        second_instruction = "基于刚才的分析，看看这个最佳产品在不同地区的表现如何"
        
        # 构建第二轮的上下文
        context_for_second = integration.context_manager.build_context_for_llm(second_instruction)
        
        print("🔍 第二轮上下文信息:")
        print(f"  📚 历史轮次: {len(context_for_second['recent_rounds'])}")
        print(f"  🔗 检测到引用: {list(context_for_second['references'].keys()) if context_for_second['references'] else '无'}")
        print(f"  📊 总轮次: {context_for_second['total_rounds']}")
        
        # 模拟第二轮代码生成（基于上下文）
        second_code = '''
# 基于第一轮分析的结果
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
best_product = product_sales.index[0]  # 复用第一轮的变量

st.subheader(f"🌍 {best_product} 在各地区的销售表现")

# 筛选最佳产品的数据
best_product_data = df[df['产品名称'] == best_product]
region_performance = best_product_data.groupby('地区')['销售额'].sum().sort_values(ascending=False)

st.bar_chart(region_performance)
st.dataframe(region_performance.reset_index())

# 地区表现分析
best_region = region_performance.index[0]
st.info(f"{best_product} 在 {best_region} 地区表现最佳: ¥{region_performance.iloc[0]:,}")
'''
        
        # 添加第二轮对话
        should_summarize_2 = integration.add_conversation_round(
            user_message=second_instruction,
            assistant_message=f"基于之前的分析，{test_data.iloc[0]['产品名称']}在不同地区的表现已分析完成。",
            code=second_code,
            execution_result={"success": True}
        )
        
        print(f"✅ 第二轮对话已添加")
        print(f"📊 是否需要摘要: {should_summarize_2}")
        
        # 第三轮对话测试
        print("\n🎯 第三轮对话测试")
        print("-" * 40)
        
        third_instruction = "现在对比一下所有产品在最佳地区的销售情况"
        
        # 构建第三轮的上下文
        context_for_third = integration.context_manager.build_context_for_llm(third_instruction)
        
        print("🔍 第三轮上下文信息:")
        print(f"  📚 历史轮次: {len(context_for_third['recent_rounds'])}")
        print(f"  🔗 检测到引用: {list(context_for_third['references'].keys()) if context_for_third['references'] else '无'}")
        
        # 模拟第三轮代码（综合前两轮的结果）
        third_code = '''
# 综合前两轮的分析结果
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
best_product = product_sales.index[0]

# 获取最佳地区（基于第二轮分析）
best_product_data = df[df['产品名称'] == best_product]
region_performance = best_product_data.groupby('地区')['销售额'].sum()
best_region = region_performance.idxmax()

st.subheader(f"🏆 所有产品在 {best_region} 地区的销售对比")

# 所有产品在最佳地区的表现
all_products_in_best_region = df[df['地区'] == best_region].groupby('产品名称')['销售额'].sum().sort_values(ascending=False)

st.bar_chart(all_products_in_best_region)
st.dataframe(all_products_in_best_region.reset_index())

# 对比分析
st.subheader("📊 对比分析结果")
for i, (product, amount) in enumerate(all_products_in_best_region.items()):
    rank = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1}."
    st.write(f"{rank} {product}: ¥{amount:,}")
'''
        
        # 添加第三轮对话
        should_summarize_3 = integration.add_conversation_round(
            user_message=third_instruction,
            assistant_message="我已经对比了所有产品在最佳地区的销售情况，提供了完整的排名分析。",
            code=third_code,
            execution_result={"success": True}
        )
        
        print(f"✅ 第三轮对话已添加")
        print(f"📊 是否需要摘要: {should_summarize_3}")
        
        # 最终统计
        final_stats = integration.get_context_stats()
        print(f"\n📈 最终统计信息:")
        print(f"  总轮次: {final_stats['total_rounds']}")
        print(f"  距离摘要: {final_stats['rounds_since_summary']}/{final_stats['summary_trigger_threshold']}")
        print(f"  内存使用: {final_stats['memory_usage_estimate']}")
        print(f"  摘要状态: {'有摘要' if final_stats['has_summary'] else '无摘要'}")
        
        # 测试摘要生成（如果接近触发条件）
        if final_stats['rounds_since_summary'] >= 3:
            print(f"\n📝 测试摘要生成")
            print("-" * 40)
            
            summary = integration.generate_conversation_summary()
            if summary:
                print(f"✅ 摘要生成成功")
                print(f"📄 摘要内容: {summary.summary_text[:100]}...")
                print(f"🎯 关键点数量: {len(summary.key_points)}")
                print(f"📊 覆盖轮次: {summary.rounds_covered}")
            else:
                print(f"❌ 摘要生成失败")
        
        print(f"\n🎉 上下文联动测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_real_context_linkage()
    print(f"\n{'='*60}")
    print(f"测试结果: {'✅ 成功' if success else '❌ 失败'}")
