#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词示例对比脚本 - 展示不同模式下的提示词差异
"""

def show_basic_prompt_example():
    """展示基础提示词示例"""
    return """你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
数据形状: (3, 6)
列名: ['日期', '产品名称', '销售额', '销量', '地区', '销售员']
数据类型:
日期      object
产品名称    object
销售额      int64
销量       int64
地区      object
销售员     object

用户指令: 分析销售数据，找出销售金额最高的产品

要求:
1. 只返回可执行的Python代码，不要解释
2. 数据已经加载在变量 df 中，不要重新读取文件
3. 使用pandas进行数据处理，直接使用 df 变量
4. 代码要简洁高效，确保语法正确
5. 使用Streamlit组件显示结果（st.write, st.dataframe, st.bar_chart等）

请生成代码:"""

def show_enhanced_prompt_example():
    """展示增强提示词示例"""
    return """你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
数据形状: (3, 6)
列名: ['日期', '产品名称', '销售额', '销量', '地区', '销售员']
数据类型:
日期      object
产品名称    object
销售额      int64
销量       int64
地区      object
销售员     object

自动提取的元数据:
列信息:
  - 日期: 类型: text
  - 产品名称: 类型: text
  - 销售额: 类型: numeric
  - 销量: 类型: numeric
  - 地区: 类型: text
  - 销售员: 类型: text

业务元数据:
表格名称: sales_data.csv
表格描述: sales_data.csv数据表，包含6个字段和20条记录，属于销售管理领域
业务领域: 销售管理

列信息:
  - 销售额 (销售额)
    描述: 销售额字段，表示相关的金额数值
    数据类型: int64
    业务含义: 财务分析和业绩评估的核心指标
    示例值: 8500, 6200, 3200
    标签: 财务, 金额, KPI

  - 产品名称 (产品名称)
    描述: 产品名称字段，表示产品或商品的标识信息
    数据类型: object
    业务含义: 产品分析和库存管理的核心标识
    示例值: 笔记本电脑, 台式电脑, 平板电脑
    标签: 产品, 标识, 分类

用户指令: 分析销售数据，找出销售金额最高的产品

要求:
1. 只返回可执行的Python代码，不要解释
2. 数据已经加载在变量 df 中，不要重新读取文件
3. 使用pandas进行数据处理，直接使用 df 变量
4. 充分利用元数据信息理解数据含义和业务语义
5. 根据列的业务含义选择合适的分析方法
6. 代码要简洁高效，确保语法正确
7. 使用Streamlit组件显示结果（st.write, st.dataframe, st.bar_chart等）

请生成代码:"""

def show_contextual_prompt_example():
    """展示上下文感知提示词示例"""
    return """你是一个专业的数据分析助手，能够理解对话历史并提供连贯的分析。

当前数据信息：
数据形状: (3, 6)
列名: ['日期', '产品名称', '销售额', '销量', '地区', '销售员']

数据元数据：
列信息:
  - 销售额: 类型: numeric, 业务含义: 财务分析核心指标
  - 产品名称: 类型: text, 业务含义: 产品标识

对话背景：用户正在分析销售数据，关注产品销售表现

用户关注点：销售额, 产品排名, 地区分布

最近的对话历史：
第1轮 - 用户：显示数据概览
第1轮 - 生成代码：
```python
st.dataframe(df.head())
st.write("数据基本信息:")
st.write(f"总记录数: {len(df)}")
```
第1轮 - 代码执行：成功

引用信息：
之前的图表代码：
```python
st.bar_chart(df.groupby("产品名称")["销售额"].sum())
```

当前用户问题：基于之前的分析，找出销售金额最高的产品并显示详细信息

请基于以上对话历史和数据信息，生成相应的Python代码。

重要指导原则：
1. 如果用户提到'之前的'、'刚才的'、'上面的'等词汇，请参考对话历史和引用信息
2. 可以基于之前的分析结果进行进一步分析或修改
3. 如果需要修改之前的代码，请生成完整的新代码
4. 确保代码能够独立运行，包含必要的导入语句
5. 保持分析的连贯性和逻辑性
6. 如果发现之前的分析有问题，可以提出改进建议"""

def analyze_prompt_differences():
    """分析不同提示词的差异"""
    basic = show_basic_prompt_example()
    enhanced = show_enhanced_prompt_example()
    contextual = show_contextual_prompt_example()
    
    print("🔍 提示词模式对比分析")
    print("=" * 80)
    
    print(f"📊 基础模式:")
    print(f"  字符数: {len(basic)}")
    print(f"  预估Token: {len(basic) // 4}")
    print(f"  主要特点: 核心约束 + 基础数据信息")
    
    print(f"\n📈 增强模式:")
    print(f"  字符数: {len(enhanced)}")
    print(f"  预估Token: {len(enhanced) // 4}")
    print(f"  主要特点: + 自动元数据 + 业务元数据")
    print(f"  增加内容: {len(enhanced) - len(basic)} 字符")
    
    print(f"\n🧠 上下文感知模式:")
    print(f"  字符数: {len(contextual)}")
    print(f"  预估Token: {len(contextual) // 4}")
    print(f"  主要特点: + 对话历史 + 引用信息 + 连贯性指导")
    print(f"  增加内容: {len(contextual) - len(basic)} 字符")
    
    print(f"\n📋 关键差异分析:")
    
    # 分析关键词出现频率
    keywords = {
        "元数据": ["元数据", "业务含义", "示例值"],
        "上下文": ["对话历史", "之前的", "引用信息"],
        "约束": ["只返回", "df变量", "Streamlit"],
        "指导": ["指导原则", "连贯性", "参考"]
    }
    
    for category, words in keywords.items():
        basic_count = sum(basic.count(word) for word in words)
        enhanced_count = sum(enhanced.count(word) for word in words)
        contextual_count = sum(contextual.count(word) for word in words)
        
        print(f"  {category}相关词汇:")
        print(f"    基础模式: {basic_count} 次")
        print(f"    增强模式: {enhanced_count} 次")
        print(f"    上下文模式: {contextual_count} 次")

def show_expected_code_quality():
    """展示不同模式下预期的代码生成质量"""
    print(f"\n🎯 预期代码生成质量对比:")
    print("-" * 50)
    
    print("📝 基础模式预期输出:")
    print("""```python
# 通用代码，可能缺乏业务理解
max_sales = df['销售额'].max()
result = df[df['销售额'] == max_sales]
st.dataframe(result)
```""")
    
    print("\n📈 增强模式预期输出:")
    print("""```python
# 理解业务含义，更精准的分析
st.subheader("💰 销售金额最高的产品分析")
max_sales_product = df.loc[df['销售额'].idxmax()]
st.metric("最高销售额", f"¥{max_sales_product['销售额']:,}")
st.write(f"产品名称: {max_sales_product['产品名称']}")
st.bar_chart(df.set_index('产品名称')['销售额'])
```""")
    
    print("\n🧠 上下文感知模式预期输出:")
    print("""```python
# 基于之前分析的连贯性代码
st.subheader("🔍 基于之前分析的详细产品信息")
# 参考之前的图表分析
sales_by_product = df.groupby("产品名称")["销售额"].sum()
max_product = sales_by_product.idxmax()
max_amount = sales_by_product.max()

st.success(f"销售金额最高的产品: {max_product}")
st.metric("销售总额", f"¥{max_amount:,}")

# 显示该产品的详细信息
product_details = df[df['产品名称'] == max_product]
st.dataframe(product_details)
```""")

if __name__ == "__main__":
    analyze_prompt_differences()
    show_expected_code_quality()
