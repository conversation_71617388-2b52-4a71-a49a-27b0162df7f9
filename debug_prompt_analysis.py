#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：分析LLM提示词构建过程
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

from core.processors.metadata_processor import MetadataProcessor
from core.metadata.metadata_manager import metadata_manager

def analyze_prompt_construction():
    """分析提示词构建过程"""
    print("🔍 分析LLM提示词构建机制")
    print("=" * 80)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑'],
        '销售额': [8500, 6200, 3200],
        '销量': [5, 3, 8],
        '地区': ['北京', '上海', '广州'],
        '销售员': ['张三', '李四', '王五']
    })
    
    # 准备数据上下文
    context = f"""数据形状: {test_data.shape}
列名: {list(test_data.columns)}
数据类型:
{test_data.dtypes.to_string()}
数据质量摘要:
- 总记录数: {len(test_data)}
- 缺失值情况: {'存在缺失值' if test_data.isnull().any().any() else '无缺失值'}
- 重复记录: {'存在重复' if test_data.duplicated().any() else '无重复'}"""
    
    # 创建处理器
    processor = MetadataProcessor()
    
    # 测试用户指令
    instruction = "分析销售数据，找出销售金额最高的产品"
    table_name = "sales_data.csv"
    
    print("📊 1. 基础提示词（无元数据）")
    print("-" * 50)
    basic_prompt = processor._build_basic_prompt(instruction, context)
    print(basic_prompt)
    print(f"\n提示词长度: {len(basic_prompt)} 字符")
    
    print("\n" + "=" * 80)
    print("📊 2. 增强提示词（含元数据）")
    print("-" * 50)
    
    # 提取自动元数据
    auto_metadata = processor.extract_dataframe_metadata(test_data, table_name)
    
    # 构建增强提示词
    enhanced_prompt = processor.enhance_prompt(instruction, context, auto_metadata, table_name)
    print(enhanced_prompt)
    print(f"\n提示词长度: {len(enhanced_prompt)} 字符")
    
    print("\n" + "=" * 80)
    print("📊 3. 提示词组成部分分析")
    print("-" * 50)
    
    # 分析各部分
    parts = {
        "角色定义": "你是Python数据分析专家",
        "数据信息": f"数据形状: {test_data.shape}",
        "用户指令": instruction,
        "代码要求": "只返回可执行的Python代码",
        "技术约束": "数据已经加载在变量 df 中",
        "输出格式": "使用Streamlit组件显示结果"
    }
    
    for part_name, part_content in parts.items():
        is_present = part_content in enhanced_prompt
        print(f"✅ {part_name}: {'包含' if is_present else '❌ 缺失'}")
    
    # 检查元数据部分
    business_metadata = metadata_manager.get_table_metadata(table_name)
    if business_metadata:
        business_context = metadata_manager.generate_llm_context(table_name)
        print(f"✅ 业务元数据: 包含 ({len(business_context)} 字符)")
        print(f"   示例值数量: {sum(1 for col in business_metadata.columns.values() if col.examples)}")
    else:
        print("❌ 业务元数据: 未配置")
    
    if auto_metadata:
        print(f"✅ 自动元数据: 包含")
        print(f"   列数: {len(auto_metadata.get('columns', {}))}")
    else:
        print("❌ 自动元数据: 未提取")
    
    return enhanced_prompt

def analyze_contextual_prompt():
    """分析上下文感知提示词"""
    print("\n" + "=" * 80)
    print("📊 4. 上下文感知提示词分析")
    print("-" * 50)
    
    # 模拟对话上下文
    conversation_context = {
        'has_summary': True,
        'summary': {
            'summary_text': '用户正在分析销售数据，关注产品销售表现',
            'key_concerns': ['销售额', '产品排名', '地区分布']
        },
        'recent_rounds': [
            {
                'user_message': '显示数据概览',
                'code': 'st.dataframe(df.head())',
                'execution_result': {'success': True}
            }
        ],
        'references': {
            '图表': {
                'success': True,
                'code': 'st.bar_chart(df.groupby("产品名称")["销售额"].sum())'
            }
        }
    }
    
    # 分析上下文感知提示词的组成部分
    contextual_parts = {
        "专业角色": "专业的数据分析助手",
        "对话背景": "用户正在分析销售数据",
        "用户关注点": "销售额, 产品排名, 地区分布",
        "历史对话": "显示数据概览",
        "引用信息": "之前的图表代码",
        "指导原则": "如果用户提到'之前的'、'刚才的'"
    }
    
    print("上下文感知提示词包含以下部分:")
    for part_name, part_desc in contextual_parts.items():
        print(f"  • {part_name}: {part_desc}")
    
    print(f"\n预估上下文感知提示词长度: 约 {len(str(conversation_context)) * 3} 字符")

def main():
    """主函数"""
    try:
        # 分析基础提示词构建
        enhanced_prompt = analyze_prompt_construction()
        
        # 分析上下文感知提示词
        analyze_contextual_prompt()
        
        print("\n" + "=" * 80)
        print("🎯 总结：LLM代码生成要求来源")
        print("-" * 50)
        print("1. 硬编码要求（核心约束）:")
        print("   • 只返回Python代码，不要解释")
        print("   • 数据在df变量中，不要重新读取")
        print("   • 使用pandas处理数据")
        print("   • 使用Streamlit组件显示结果")
        print()
        print("2. 元数据驱动要求:")
        print("   • 根据列的业务含义选择分析方法")
        print("   • 充分利用示例值理解数据语义")
        print()
        print("3. 上下文感知要求:")
        print("   • 基于对话历史保持连贯性")
        print("   • 参考之前的分析结果")
        print("   • 处理用户的引用和修改请求")
        print()
        print("4. 动态生成要求:")
        print("   • 根据数据特征调整分析策略")
        print("   • 基于用户指令选择合适的可视化")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
