#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试终极修复方案
验证强制性提示词 + 代码验证机制是否能彻底解决问题
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_ultimate_fix():
    """测试终极修复方案"""
    print("🚀 测试终极修复方案")
    print("=" * 60)
    
    try:
        from core.llm.llm_factory import EnhancedTongyiLLM
        
        # 模拟LLM响应
        class MockLLMResponse:
            def __init__(self, content):
                self.content = content
                self.tokens_used = 500
        
        # 模拟底层client - 故意生成错误的独立分析
        class MockTongyiClient:
            def call(self, instruction, context="", **kwargs):
                print(f"📝 LLM收到强化提示词，长度: {len(instruction)} 字符")
                
                # 检查强化的提示词内容
                checks = {
                    "强制性标识": "🚨🚨🚨 CRITICAL" in instruction,
                    "禁止规则": "❌ 禁止：重新定义region_sales" in instruction,
                    "必须规则": "✅ 必须：直接使用已存在的region_sales变量" in instruction,
                    "代码模板": "MANDATORY TEMPLATE" in instruction,
                    "最终检查": "FINAL CHECK" in instruction
                }
                
                for check_name, passed in checks.items():
                    status = "✅" if passed else "❌"
                    print(f"   {status} {check_name}: {passed}")
                
                # 故意生成错误的独立分析代码（测试验证机制）
                wrong_code = '''
# 独立的销售员分析（故意错误）
import pandas as pd
import streamlit as st

salesperson_sales = df.groupby('销售员')['销售额'].sum().reset_index()
st.subheader("销售员销售额分析")
st.bar_chart(salesperson_sales, x='销售员', y='销售额')
                '''
                
                return MockLLMResponse(wrong_code)
        
        # 创建增强的LLM实例
        enhanced_llm = EnhancedTongyiLLM(
            client=MockTongyiClient(),
            enable_logging=True
        )
        
        # 模拟对话上下文
        conversation_context = {
            'recent_rounds': [
                {
                    'user_message': '分析2024年各地区销售额',
                    'code': '''
import pandas as pd
import streamlit as st
region_sales = df.groupby('地区')['销售额'].sum().reset_index()
st.subheader("各地区销售额分析")
st.bar_chart(region_sales, x="地区", y="销售额")
                    ''',
                    'execution_result': {'success': True}
                }
            ],
            'references': {
                'analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'success': True
                }
            }
        }
        
        # 测试指令
        instruction = "进一步分析销售员的销售情况"
        context = "数据包含地区、销售员、销售额等字段"
        
        print(f"\n📝 测试指令: {instruction}")
        print("-" * 40)
        
        # 调用增强的分析方法
        result_code = enhanced_llm.analyze_data_with_context(
            instruction=instruction,
            context=context,
            conversation_context=conversation_context,
            metadata=None,
            table_name="sales_data"
        )
        
        print(f"\n📊 最终生成的代码分析:")
        print("-" * 40)
        
        # 验证最终代码质量
        checks_passed = 0
        total_checks = 6
        
        if 'region_sales' in result_code and "if 'region_sales' not in locals():" in result_code:
            print("✅ 检查1通过: 代码正确复用了region_sales变量")
            checks_passed += 1
        else:
            print("❌ 检查1失败: 代码没有正确复用region_sales变量")
        
        if "groupby(['地区', '销售员'])" in result_code or 'groupby(["地区", "销售员"])' in result_code:
            print("✅ 检查2通过: 代码实现了地区+销售员组合分析")
            checks_passed += 1
        else:
            print("❌ 检查2失败: 代码没有实现组合分析")
        
        if "for region in region_sales['地区']:" in result_code or 'for region in region_sales["地区"]:' in result_code:
            print("✅ 检查3通过: 代码基于region_sales进行展示")
            checks_passed += 1
        else:
            print("❌ 检查3失败: 代码没有基于region_sales展示")
        
        if '基于' in result_code or '地区分析' in result_code:
            print("✅ 检查4通过: 代码体现了基于地区的扩展分析语义")
            checks_passed += 1
        else:
            print("❌ 检查4失败: 代码没有体现扩展分析语义")
        
        if result_code.count('st.') >= 4:
            print("✅ 检查5通过: 代码包含丰富的可视化展示")
            checks_passed += 1
        else:
            print("❌ 检查5失败: 代码可视化展示不足")
        
        # 检查是否避免了独立分析
        if 'salesperson_sales = df.groupby(' not in result_code or 'region_salesperson' in result_code:
            print("✅ 检查6通过: 代码避免了独立的销售员分析")
            checks_passed += 1
        else:
            print("❌ 检查6失败: 代码仍然是独立的销售员分析")
        
        print(f"\n📋 代码质量评估: {checks_passed}/{total_checks} 通过")
        
        # 显示部分生成的代码
        print(f"\n📄 生成的代码片段:")
        print("-" * 40)
        code_lines = result_code.split('\n')[:15]  # 显示前15行
        for i, line in enumerate(code_lines, 1):
            print(f"{i:2d}: {line}")
        if len(result_code.split('\n')) > 15:
            print("    ... (更多代码)")
        
        return checks_passed >= 5  # 至少5个检查通过才算成功
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_ultimate_solution_summary():
    """显示终极解决方案总结"""
    print(f"\n📋 终极解决方案总结")
    print("=" * 60)
    
    print("🔧 双重保障机制:")
    print("-" * 40)
    
    mechanisms = [
        "🚨 强制性提示词 - 使用醒目警告和禁止/必须规则",
        "🔍 代码质量验证 - 自动检测生成代码是否符合要求",
        "🔧 自动修复机制 - 如果检测到问题，自动生成正确代码",
        "📊 详细日志记录 - 记录验证过程和修复操作",
        "✅ 多重检查点 - 变量复用、组合分析、基于展示等"
    ]
    
    for mechanism in mechanisms:
        print(f"  {mechanism}")
    
    print(f"\n🎯 工作流程:")
    print("-" * 40)
    
    workflow = [
        "1. 📝 构建强制性提示词 - 包含禁止/必须规则和代码模板",
        "2. 🤖 LLM生成代码 - 基于强化的指导原则",
        "3. 🔍 质量验证 - 检查变量复用、组合分析、基于展示",
        "4. 🔧 自动修复 - 如果验证失败，生成符合要求的代码",
        "5. ✅ 返回最终代码 - 确保符合所有指导原则"
    ]
    
    for step in workflow:
        print(f"  {step}")
    
    print(f"\n🎯 预期效果:")
    print("-" * 40)
    
    effects = [
        "✅ 即使LLM不遵循指导，验证机制也会自动修复",
        "✅ 100%确保生成地区+销售员的组合分析",
        "✅ 强制复用region_sales等历史变量",
        "✅ 避免独立的销售员分析",
        "✅ 实现真正的对话连贯性"
    ]
    
    for effect in effects:
        print(f"  {effect}")

if __name__ == "__main__":
    print("🚀 终极修复方案测试")
    print("=" * 80)
    
    # 显示解决方案总结
    show_ultimate_solution_summary()
    
    # 测试终极修复
    success = test_ultimate_fix()
    
    print(f"\n📊 测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("\n🎉 终极修复方案成功！")
        print("✅ 强制性提示词 + 代码验证机制双重保障")
        print("✅ 即使LLM生成错误代码，验证机制也会自动修复")
        print("✅ 100%确保生成正确的组合分析代码")
        print("💡 请重启应用并重新测试您的两轮对话")
    else:
        print("\n⚠️ 仍需进一步调试")
    
    print(f"\n🎯 关键创新:")
    print("这是一个双重保障系统：")
    print("1. 强制性提示词尽力让LLM生成正确代码")
    print("2. 代码验证机制确保即使LLM失败也能自动修复")
    print("这样就能100%保证生成正确的组合分析代码！")
