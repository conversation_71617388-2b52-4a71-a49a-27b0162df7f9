# 🔍 连续追问上下文日志查看指南

## 📋 概述

现在系统已经集成了详细的上下文日志功能，您可以清楚地看到：
- 每次生成的对话摘要详情
- 发送给大模型的完整提示词
- 上下文构建过程
- 对话轮次管理

---

## 🛠️ 日志查看工具使用

### 1. 查看日志统计摘要
```bash
python view_context_logs.py --summary
```
显示：
- 日志文件信息
- 生成摘要次数
- 构建上下文次数
- 发送提示词次数
- 添加对话轮次数

### 2. 查看完整日志
```bash
python view_context_logs.py
```
显示所有日志内容

### 3. 只查看上下文相关日志
```bash
python view_context_logs.py --context-only
```
过滤出上下文管理相关的日志

### 4. 实时监控日志
```bash
python view_context_logs.py --follow --context-only
```
实时跟踪新的上下文日志（按Ctrl+C退出）

### 5. 指定日志文件
```bash
python view_context_logs.py --file logs/app_20250805.log --context-only
```

---

## 📝 日志内容说明

### 1. 对话摘要生成日志
```
================================================================================
📝 生成的对话摘要详情:
  用户身份/场景: 数据分析师
  核心目标: 数据可视化
  关键关注点: 数据质量, 可视化效果
  当前进展: 进展顺利，大部分分析成功完成
  覆盖轮次: 3
  关键点数量: 2
  摘要文本: 数据分析师正在进行数据可视化，重点关注数据质量, 可视化效果，进展顺利，大部分分析成功完成。已完成3轮对话，生成3段代码。
  关键对话点:
    1. 第1轮: 请分析销售数据的趋势...
    2. 第2轮: 修改图表颜色为蓝色...
================================================================================
```

### 2. 上下文构建日志
```
================================================================================
🧠 为LLM构建的上下文信息:
  当前指令: 基于刚才的分析，预测下个月的销售额
  总对话轮次: 5
  是否有摘要: True
  📝 使用的摘要信息:
    摘要文本: 数据分析师正在进行数据可视化...
    用户身份: 数据分析师
    核心目标: 数据可视化
    关注点: 数据质量, 可视化效果
  📚 最近对话轮次数: 3
  最近对话内容:
    第1轮 - 用户: 请分析销售数据的趋势...
    第1轮 - 代码: df.plot()...
    第1轮 - 执行: 成功
    第2轮 - 用户: 修改图表颜色...
    第2轮 - 代码: plt.plot(color='blue')...
    第2轮 - 执行: 成功
  🔗 检测到的引用: ['chart']
    chart: plt.plot(color='blue')... (执行成功)
================================================================================
```

### 3. 发送给大模型的提示词日志
```
====================================================================================================
🤖 发送给大模型的完整提示词:
----------------------------------------------------------------------------------------------------
你是一个专业的数据分析助手，能够理解对话历史并提供连贯的分析。

当前数据信息：
DataFrame形状: (200, 6)
列名: ['日期', '地区', '产品', '销售额', '销售量', '客户数']
数据类型: 日期(datetime), 地区(object), 产品(object), 销售额(float64), 销售量(int64), 客户数(int64)

对话背景：数据分析师正在进行数据可视化，重点关注数据质量, 可视化效果，进展顺利，大部分分析成功完成。已完成3轮对话，生成3段代码。

用户关注点：数据质量, 可视化效果

最近的对话历史：
第1轮 - 用户：请分析销售数据的趋势
第1轮 - 生成代码：
```python
df.plot()
```
第1轮 - 代码执行：成功

引用信息：
之前的chart代码：
```python
plt.plot(color='blue')
```

当前用户问题：基于刚才的分析，预测下个月的销售额

请基于以上对话历史和数据信息，生成相应的Python代码。

重要指导原则：
1. 如果用户提到'之前的'、'刚才的'、'上面的'等词汇，请参考对话历史和引用信息
2. 可以基于之前的分析结果进行进一步分析或修改
3. 如果需要修改之前的代码，请生成完整的新代码
4. 确保代码能够独立运行，包含必要的导入语句
5. 保持分析的连贯性和逻辑性
6. 如果发现之前的分析有问题，可以提出改进建议
----------------------------------------------------------------------------------------------------
提示词总长度: 1234 字符
预估Token数: 308 tokens
====================================================================================================
```

### 4. 对话轮次添加日志
```
================================================================================
💬 添加新的对话轮次:
  用户消息: 基于刚才的分析，预测下个月的销售额
  助手回复: 我来基于之前的销售趋势分析，为您预测下个月的销售额...
  生成代码: from sklearn.linear_model import LinearRegression
import numpy as np
...
  执行结果: 成功
================================================================================
📊 对话轮次添加完成，是否需要生成摘要: True
```

---

## 🎯 使用场景

### 1. 调试连续追问功能
当AI无法正确理解上下文时，查看日志可以帮助您：
- 确认摘要是否正确生成
- 检查上下文信息是否完整
- 验证引用检测是否准确

### 2. 优化提示词效果
通过查看发送给大模型的完整提示词：
- 了解上下文信息的组织方式
- 评估提示词的质量和长度
- 分析Token使用情况

### 3. 性能分析
监控日志可以帮助您：
- 跟踪摘要生成频率
- 观察上下文大小变化
- 分析系统性能表现

### 4. 功能验证
验证连续追问功能是否正常工作：
- 确认对话轮次正确记录
- 检查摘要触发机制
- 验证引用跟踪准确性

---

## 🔧 实际操作步骤

### 步骤1: 启动应用
```bash
streamlit run streamlit_app.py
```

### 步骤2: 开启调试模式
在Streamlit界面的侧边栏中，勾选"🔍 显示调试信息"

### 步骤3: 进行连续对话
1. 上传数据文件
2. 进行第一轮分析："请分析销售数据"
3. 进行连续追问："基于刚才的分析，显示趋势图"
4. 继续追问："修改之前的图表颜色"

### 步骤4: 查看详细日志
在另一个终端窗口中运行：
```bash
# 实时监控上下文日志
python view_context_logs.py --follow --context-only
```

### 步骤5: 分析日志内容
观察以下关键信息：
- 摘要何时生成，内容是否准确
- 上下文构建是否包含正确信息
- 引用检测是否识别到"之前的"、"刚才的"等词汇
- 发送给大模型的提示词是否合理

---

## 📊 日志分析技巧

### 1. 关注摘要质量
- 用户身份识别是否准确
- 核心目标提取是否正确
- 关注点是否符合实际对话内容

### 2. 检查上下文完整性
- 最近对话轮次是否包含关键信息
- 引用检测是否准确
- 摘要信息是否有效传递

### 3. 评估提示词效果
- 提示词长度是否合理（建议<8000字符）
- 信息组织是否清晰
- 指导原则是否明确

### 4. 监控性能指标
- 摘要生成频率（建议每3-5轮）
- Token使用量
- 响应时间

---

## 🎉 总结

通过详细的日志系统，您现在可以：
- ✅ 实时监控连续追问功能的工作状态
- ✅ 深入了解每次摘要生成的详细内容
- ✅ 查看发送给大模型的完整上下文信息
- ✅ 调试和优化连续对话的效果
- ✅ 分析系统性能和Token使用情况

**开始使用日志工具，深入了解您的AI助手是如何理解和处理连续对话的！**
