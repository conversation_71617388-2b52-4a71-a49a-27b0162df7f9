#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试元数据配置传递过程
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_environment_variables():
    """调试环境变量"""
    print("🔍 检查环境变量...")
    
    metadata_vars = [
        'ENABLE_METADATA',
        'TONGYI_API_KEY',
        'ENABLE_CHART_FIX',
        'ENABLE_LOGGING'
    ]
    
    for var in metadata_vars:
        value = os.getenv(var)
        print(f"  {var}: {value if value else '未设置'}")

def debug_config_loading():
    """调试配置加载过程"""
    print("\n🔍 检查配置加载...")
    
    try:
        from config.app_settings import get_config
        app_config = get_config()
        
        print(f"  LLM配置:")
        print(f"    enable_chart_fix: {app_config.llm.enable_chart_fix}")
        print(f"    enable_metadata: {app_config.llm.enable_metadata}")
        print(f"    enable_logging: {app_config.llm.enable_logging}")
        
        return app_config
        
    except Exception as e:
        print(f"  ❌ 配置加载失败: {e}")
        return None

def debug_tongyi_config():
    """调试TongyiConfig"""
    print("\n🔍 检查TongyiConfig...")
    
    try:
        from core.utils.config import TongyiConfig
        
        # 检查默认值
        print("  默认配置:")
        default_config = TongyiConfig(api_key="test")
        print(f"    enable_metadata: {default_config.enable_metadata}")
        
        # 检查from_env
        print("  from_env配置:")
        try:
            env_config = TongyiConfig.from_env()
            print(f"    enable_metadata: {env_config.enable_metadata}")
        except Exception as e:
            print(f"    ❌ from_env失败: {e}")
            
    except Exception as e:
        print(f"  ❌ TongyiConfig导入失败: {e}")

def debug_llm_factory():
    """调试LLM工厂"""
    print("\n🔍 检查LLM工厂...")
    
    try:
        from core.llm.llm_factory import LLMFactory
        from core.utils.config import TongyiConfig
        
        # 测试默认参数
        print("  LLMFactory.create_tongyi_llm 默认参数:")
        import inspect
        sig = inspect.signature(LLMFactory.create_tongyi_llm)
        for param_name, param in sig.parameters.items():
            if param.default != inspect.Parameter.empty:
                print(f"    {param_name}: {param.default}")
        
    except Exception as e:
        print(f"  ❌ LLM工厂检查失败: {e}")

def debug_streamlit_integration():
    """调试Streamlit集成"""
    print("\n🔍 检查Streamlit集成...")
    
    try:
        from core.integrations.streamlit_integration import StreamlitLLMIntegration
        
        # 创建集成实例
        integration = StreamlitLLMIntegration()
        
        # 检查自动初始化逻辑
        print("  自动初始化逻辑检查...")
        
        # 模拟配置检查
        try:
            from config.app_settings import get_config
            app_config = get_config()
            api_key = app_config.llm.tongyi_api_key
            
            if api_key:
                print(f"    API密钥存在: {api_key[:10]}...")
                print(f"    配置中的enable_metadata: {app_config.llm.enable_metadata}")
            else:
                print("    API密钥不存在，将使用环境变量")
                
        except Exception as e:
            print(f"    配置检查失败: {e}")
            
    except Exception as e:
        print(f"  ❌ Streamlit集成检查失败: {e}")

def test_manual_llm_creation():
    """测试手动创建LLM"""
    print("\n🧪 测试手动创建LLM...")
    
    try:
        from core.llm.llm_factory import LLMFactory
        from core.utils.config import TongyiConfig
        
        # 创建配置
        config = TongyiConfig(
            api_key="test_key",
            enable_metadata=True
        )
        
        print(f"  创建的配置:")
        print(f"    enable_metadata: {config.enable_metadata}")
        
        # 尝试创建LLM（不会真正调用API）
        print("  调用LLMFactory.create_tongyi_llm...")
        print(f"    传入参数: enable_metadata=True")
        
        # 这里不实际创建，只是检查参数传递
        import inspect
        sig = inspect.signature(LLMFactory.create_tongyi_llm)
        print(f"    函数签名: {sig}")
        
    except Exception as e:
        print(f"  ❌ 手动创建测试失败: {e}")

def set_environment_variable():
    """设置环境变量"""
    print("\n🔧 设置环境变量...")
    
    os.environ['ENABLE_METADATA'] = 'true'
    print("  已设置 ENABLE_METADATA=true")
    
    # 验证设置
    value = os.getenv('ENABLE_METADATA')
    print(f"  验证: ENABLE_METADATA={value}")

if __name__ == "__main__":
    print("🔧 开始调试元数据配置...")
    print("=" * 60)
    
    # 1. 检查环境变量
    debug_environment_variables()
    
    # 2. 检查配置加载
    app_config = debug_config_loading()
    
    # 3. 检查TongyiConfig
    debug_tongyi_config()
    
    # 4. 检查LLM工厂
    debug_llm_factory()
    
    # 5. 检查Streamlit集成
    debug_streamlit_integration()
    
    # 6. 测试手动创建
    test_manual_llm_creation()
    
    # 7. 设置环境变量
    set_environment_variable()
    
    print("=" * 60)
    print("🎯 调试完成！")
    print("\n💡 建议:")
    print("1. 设置环境变量 ENABLE_METADATA=true")
    print("2. 重启Streamlit应用")
    print("3. 检查日志中的元数据功能状态")
