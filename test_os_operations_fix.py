#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试os操作移除修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.processors.code_cleaner import CodeCleaner

def test_os_operations_removal():
    """测试os操作移除功能"""
    print("🧪 测试os操作移除功能...")
    
    # 包含os操作的测试代码
    test_code = """
import pandas as pd
import os
from datetime import datetime

# 分析数据
df = pd.read_csv('data.csv')
sales_by_region = df.groupby('region')['sales'].sum()

# 保存图表数据
chart_data_dir = "charts"
os.makedirs(chart_data_dir, exist_ok=True)

# 保存文件
if 'df' in locals():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    chart_file = os.path.join(chart_data_dir, f"chart_data_{timestamp}.csv")
    df.to_csv(chart_file, index=False)
    print(f"图表数据已保存到: {chart_file}")

# 显示结果
st.bar_chart(sales_by_region)
"""
    
    print("原始代码:")
    print("-" * 40)
    print(test_code)
    print("-" * 40)
    
    # 创建代码清理器
    cleaner = CodeCleaner()
    
    # 清理代码
    cleaned_code = cleaner.clean(test_code)
    
    print("\n清理后的代码:")
    print("-" * 40)
    print(cleaned_code)
    print("-" * 40)
    
    # 检查结果
    success = True
    
    if 'import os' in cleaned_code:
        print("❌ 失败: 仍然包含 'import os'")
        success = False
    else:
        print("✅ 成功: 已移除 'import os'")
    
    if 'os.makedirs' in cleaned_code:
        print("❌ 失败: 仍然包含 'os.makedirs'")
        success = False
    else:
        print("✅ 成功: 已移除 'os.makedirs'")
    
    if 'os.path.join' in cleaned_code:
        print("❌ 失败: 仍然包含 'os.path.join'")
        success = False
    else:
        print("✅ 成功: 已移除 'os.path.join'")
    
    if '代码生成失败' in cleaned_code:
        print("❌ 失败: 代码被标记为生成失败")
        success = False
    else:
        print("✅ 成功: 代码未被标记为失败")
    
    if 'st.bar_chart' in cleaned_code:
        print("✅ 成功: 保留了正常的代码")
    else:
        print("❌ 失败: 丢失了正常的代码")
        success = False
    
    return success

def test_code_execution():
    """测试清理后的代码执行"""
    print("\n🧪 测试清理后的代码执行...")
    
    # 包含os操作的代码
    test_code = """
import pandas as pd
import os
from datetime import datetime

# 创建测试数据
df = pd.DataFrame({'region': ['北京', '上海'], 'sales': [100, 200]})
sales_by_region = df.groupby('region')['sales'].sum()

# 尝试保存文件（这部分会被移除）
chart_data_dir = "charts"
os.makedirs(chart_data_dir, exist_ok=True)
chart_file = os.path.join(chart_data_dir, "test.csv")
df.to_csv(chart_file, index=False)

# 显示结果
print("销售数据分析完成")
print(sales_by_region)
"""
    
    cleaner = CodeCleaner()
    cleaned_code = cleaner.clean(test_code)
    
    try:
        # 准备执行环境
        import pandas as pd
        from datetime import datetime
        
        exec_globals = {
            'pd': pd,
            'datetime': datetime,
        }
        
        # 执行清理后的代码
        exec(cleaned_code, exec_globals)
        print("✅ 清理后的代码执行成功")
        return True
    except Exception as e:
        print(f"❌ 清理后的代码执行失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 开始测试os操作移除修复...")
    
    success1 = test_os_operations_removal()
    success2 = test_code_execution()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！os操作移除修复成功。")
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
