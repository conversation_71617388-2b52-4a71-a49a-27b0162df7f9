#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试元数据是否被添加到提示词中
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.llm.llm_factory import LLMFactory
from core.utils.config import TongyiConfig
from core.processors.metadata_processor import MetadataProcessor

def test_metadata_in_prompt():
    """测试元数据是否被添加到提示词中"""
    print("🧪 测试元数据是否被添加到提示词中...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '销售额': [8500, 6200, 3200],
        '地区': ['北京', '上海', '广州'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑']
    })
    
    # 创建元数据处理器
    processor = MetadataProcessor()
    
    # 提取元数据
    metadata = processor.extract_dataframe_metadata(test_data, "sales_data")
    
    # 构建基础上下文
    context = f"""数据形状: {test_data.shape}
列名: {list(test_data.columns)}
前3行数据:
{test_data.head(3).to_string()}"""
    
    instruction = "分析各地区销售情况"
    
    # 测试1: 不使用元数据的提示词
    print("\n📝 不使用元数据的提示词:")
    basic_prompt = processor.enhance_prompt(instruction, context, None, "sales_data")
    print(f"长度: {len(basic_prompt)} 字符")
    print("前200字符:")
    print(basic_prompt[:200] + "...")
    
    # 测试2: 使用元数据的提示词
    print("\n📝 使用元数据的提示词:")
    enhanced_prompt = processor.enhance_prompt(instruction, context, metadata, "sales_data")
    print(f"长度: {len(enhanced_prompt)} 字符")
    print("前200字符:")
    print(enhanced_prompt[:200] + "...")
    
    # 检查差异
    print(f"\n📊 提示词长度对比:")
    print(f"  基础提示词: {len(basic_prompt)} 字符")
    print(f"  增强提示词: {len(enhanced_prompt)} 字符")
    print(f"  增加了: {len(enhanced_prompt) - len(basic_prompt)} 字符")
    
    # 检查是否包含元数据信息
    metadata_keywords = ['📊 列信息分析', '业务含义', '推荐分析', '类型=']
    found_keywords = []
    for keyword in metadata_keywords:
        if keyword in enhanced_prompt:
            found_keywords.append(keyword)
    
    print(f"\n🔍 元数据关键词检查:")
    print(f"  找到的关键词: {found_keywords}")
    print(f"  覆盖率: {len(found_keywords)}/{len(metadata_keywords)} ({len(found_keywords)/len(metadata_keywords)*100:.1f}%)")
    
    # 显示元数据部分
    if '自动提取的元数据:' in enhanced_prompt:
        start_idx = enhanced_prompt.find('自动提取的元数据:')
        end_idx = enhanced_prompt.find('用户指令:', start_idx)
        if end_idx == -1:
            end_idx = len(enhanced_prompt)
        metadata_section = enhanced_prompt[start_idx:end_idx]
        print(f"\n📋 提取的元数据部分:")
        print("-" * 50)
        print(metadata_section[:500] + ("..." if len(metadata_section) > 500 else ""))
        print("-" * 50)
    
    return len(found_keywords) >= 2  # 至少找到2个关键词算成功

def test_llm_factory_metadata():
    """测试LLM工厂的元数据功能"""
    print("\n🧪 测试LLM工厂的元数据功能...")
    
    try:
        # 创建配置
        config = TongyiConfig.from_env()
        print(f"配置中的元数据设置: {config.enable_metadata}")
        
        # 创建LLM实例
        llm = LLMFactory.create_tongyi_llm(
            config=config,
            enable_metadata=True  # 强制启用
        )
        
        print(f"LLM实例的元数据设置: {llm.enable_metadata}")
        
        return llm.enable_metadata
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 开始测试元数据提示词功能...")
    print("=" * 60)
    
    # 测试1: 元数据在提示词中
    success1 = test_metadata_in_prompt()
    
    # 测试2: LLM工厂元数据功能
    success2 = test_llm_factory_metadata()
    
    print("=" * 60)
    if success1 and success2:
        print("🎉 元数据提示词功能测试通过！")
        print("✨ 元数据信息已成功添加到LLM提示词中。")
    else:
        print("❌ 元数据提示词功能测试失败。")
        print(f"  提示词测试: {'✅' if success1 else '❌'}")
        print(f"  LLM工厂测试: {'✅' if success2 else '❌'}")
