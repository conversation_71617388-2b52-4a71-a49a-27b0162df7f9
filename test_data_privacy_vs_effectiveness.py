#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据隐私保护与分析效果的平衡
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_scenarios():
    """创建不同的数据展示场景"""
    
    # 原始敏感数据
    sensitive_data = pd.DataFrame({
        '销售额': [8500, 6200, 3200, 4500, 9200],
        '地区': ['北京', '上海', '广州', '深圳', '北京'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑', '手机', '笔记本电脑'],
        '销量': [5, 3, 8, 15, 6],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
        '销售员': ['张三', '李四', '王五', '赵六', '张三']
    })
    
    return sensitive_data

def scenario_1_full_data(data):
    """场景1: 完整数据（当前方式）"""
    context = f"""数据形状: {data.shape}
列名: {list(data.columns)}

数据类型:
{data.dtypes.to_string()}

前5行数据:
{data.head().to_string()}

数值列统计:
{data.describe().to_string()}"""
    
    return context, "完整数据展示"

def scenario_2_anonymized_samples(data):
    """场景2: 匿名化样例数据"""
    # 创建匿名化的样例
    anonymized_data = data.copy()
    anonymized_data['销售额'] = ['****', '****', '****', '****', '****']
    anonymized_data['地区'] = ['地区A', '地区B', '地区C', '地区D', '地区A']
    anonymized_data['产品名称'] = ['产品A', '产品B', '产品C', '产品D', '产品A']
    anonymized_data['销售员'] = ['员工A', '员工B', '员工C', '员工D', '员工A']
    
    context = f"""数据形状: {data.shape}
列名: {list(data.columns)}

数据类型:
{data.dtypes.to_string()}

匿名化样例数据:
{anonymized_data.to_string()}

数值列统计范围:
销售额: 数值型，范围 [最小值-最大值]
销量: 数值型，范围 [最小值-最大值]"""
    
    return context, "匿名化样例数据"

def scenario_3_schema_only(data):
    """场景3: 仅结构信息"""
    context = f"""数据形状: {data.shape}
列名: {list(data.columns)}

数据类型:
{data.dtypes.to_string()}

列描述:
- 销售额: 数值型，销售金额
- 地区: 文本型，销售地区
- 产品名称: 文本型，产品名称
- 销量: 数值型，销售数量
- 日期: 文本型，交易日期
- 销售员: 文本型，销售人员"""
    
    return context, "仅结构信息"

def scenario_4_metadata_enhanced(data):
    """场景4: 元数据增强（无样例数据）"""
    context = f"""数据形状: {data.shape}
列名: {list(data.columns)}

数据类型:
{data.dtypes.to_string()}

元数据信息:
列信息:
  - 销售额: 类型: numeric, 业务含义: 核心收入指标, 分析用途: 收入分析、趋势预测
  - 地区: 类型: text, 业务含义: 地理维度, 分析用途: 区域对比、地域分布
  - 产品名称: 类型: text, 业务含义: 产品维度, 分析用途: 产品分析、品类对比
  - 销量: 类型: numeric, 业务含义: 销售规模指标, 分析用途: 销量分析、库存管理
  - 日期: 类型: text, 业务含义: 时间维度, 分析用途: 时间序列分析、趋势分析
  - 销售员: 类型: text, 业务含义: 人员维度, 分析用途: 绩效分析、团队管理

业务上下文:
- 表格类型: 销售交易记录
- 业务领域: 销售管理
- 分析目标: 销售业绩分析、趋势预测、区域对比

推荐分析方法:
- 按地区的销售额对比
- 产品销售排行分析
- 时间趋势分析
- 销售员绩效分析"""
    
    return context, "元数据增强（无样例）"

def scenario_5_statistical_summary(data):
    """场景5: 统计摘要（无原始数据）"""
    context = f"""数据形状: {data.shape}
列名: {list(data.columns)}

数据类型:
{data.dtypes.to_string()}

统计摘要:
数值列分布特征:
- 销售额: 连续数值，存在较大差异，适合分组分析
- 销量: 离散数值，变化范围较大，可用于规模分析

分类列特征:
- 地区: 5个不同值，存在重复，适合分组统计
- 产品名称: 4个不同值，存在重复，适合品类分析
- 销售员: 4个不同值，存在重复，适合绩效分析
- 日期: 5个连续日期，适合时间序列分析

数据质量:
- 无缺失值
- 数据类型一致
- 时间序列连续"""
    
    return context, "统计摘要（无原始数据）"

def analyze_scenarios():
    """分析不同场景的效果"""
    print("🔍 分析不同数据展示场景...")
    
    data = create_test_scenarios()
    
    scenarios = [
        scenario_1_full_data(data),
        scenario_2_anonymized_samples(data),
        scenario_3_schema_only(data),
        scenario_4_metadata_enhanced(data),
        scenario_5_statistical_summary(data)
    ]
    
    print("\n📊 场景对比分析:")
    print("=" * 80)
    
    for i, (context, name) in enumerate(scenarios, 1):
        print(f"\n🔸 场景{i}: {name}")
        print(f"   信息长度: {len(context)} 字符")
        
        # 安全性评估
        security_score = 0
        if '张三' not in context and '8500' not in context:
            security_score += 3  # 无敏感数据
        if '****' in context or '地区A' in context:
            security_score += 2  # 匿名化处理
        if '业务含义' in context or '分析用途' in context:
            security_score += 1  # 有业务指导
        
        # 实用性评估
        utility_score = 0
        if '数据类型' in context:
            utility_score += 1  # 基础结构信息
        if '业务含义' in context:
            utility_score += 2  # 业务语义
        if '推荐分析' in context:
            utility_score += 2  # 分析指导
        if '统计' in context or '分布' in context:
            utility_score += 1  # 数据特征
        
        print(f"   安全性评分: {security_score}/6 ⭐")
        print(f"   实用性评分: {utility_score}/6 ⭐")
        print(f"   综合评分: {(security_score + utility_score)/2:.1f}/6 ⭐")
        
        # 显示部分内容
        preview = context[:200] + "..." if len(context) > 200 else context
        print(f"   内容预览: {preview}")

def recommend_best_practice():
    """推荐最佳实践"""
    print("\n\n💡 最佳实践推荐:")
    print("=" * 80)
    
    print("🎯 推荐方案: 场景4 + 场景5 的组合")
    print("\n✅ 优势:")
    print("  🔒 数据安全: 不暴露任何敏感的原始数据")
    print("  🧠 智能分析: 提供丰富的业务语义和分析指导")
    print("  📊 数据理解: 通过统计特征了解数据分布")
    print("  🎯 分析精准: 基于业务含义生成更准确的分析代码")
    
    print("\n🔧 实施建议:")
    print("  1. 移除所有原始数据样例")
    print("  2. 保留数据结构信息（列名、类型、形状）")
    print("  3. 增强元数据信息（业务含义、分析用途）")
    print("  4. 提供统计特征摘要（无具体数值）")
    print("  5. 添加数据质量信息（完整性、一致性）")
    
    print("\n⚠️ 注意事项:")
    print("  • 确保元数据信息不包含敏感业务逻辑")
    print("  • 定期审查和更新元数据模板")
    print("  • 建立数据脱敏的标准流程")
    print("  • 考虑不同敏感级别的数据处理策略")

if __name__ == "__main__":
    print("🔐 数据隐私保护与分析效果平衡测试")
    print("=" * 80)
    
    analyze_scenarios()
    recommend_best_practice()
    
    print("\n🎉 结论: 通过元数据增强和统计摘要，可以在保护数据隐私的同时")
    print("     提供足够的信息让大模型生成高质量的分析代码！")
