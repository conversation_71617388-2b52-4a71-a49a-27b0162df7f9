#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终完整修复测试
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.integrations.streamlit_integration import StreamlitLLMIntegration

def test_complete_conversation_flow():
    """测试完整的多轮对话流程"""
    print("🧪 测试完整的多轮对话流程...")
    
    # 创建集成实例
    integration = StreamlitLLMIntegration()
    
    # 自动初始化LLM
    success = integration.auto_initialize_llm()
    if not success:
        print("❌ LLM初始化失败")
        return False
    
    print("✅ LLM初始化成功")
    
    # 加载测试数据
    test_data = pd.DataFrame({
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑', '手机', '笔记本电脑'],
        '销售额': [8500, 6200, 3200, 4500, 9200],
        '销量': [5, 3, 8, 15, 6],
        '地区': ['北京', '上海', '广州', '深圳', '北京'],
        '销售员': ['张三', '李四', '王五', '赵六', '张三']
    })
    
    success, error_msg = integration.load_data(test_data, "sales_data")
    if not success:
        print(f"❌ 数据加载失败: {error_msg}")
        return False
    
    print("✅ 数据加载成功")
    
    # 测试多轮对话
    conversations = [
        {
            "round": 1,
            "instruction": "分析2024年各地区销售额",
            "expected_keywords": ["地区", "销售额"]
        },
        {
            "round": 2,
            "instruction": "在这基础上分析各销售员的销售额",
            "expected_keywords": ["销售员", "销售额"]
        },
        {
            "round": 3,
            "instruction": "请将图形换成饼图",
            "expected_keywords": ["pie", "饼图"]
        }
    ]
    
    total_checks = 0
    passed_checks = 0
    
    for conv in conversations:
        round_num = conv["round"]
        instruction = conv["instruction"]
        expected_keywords = conv["expected_keywords"]
        
        print(f"\n🎯 第{round_num}轮对话测试: {instruction}")
        
        # 执行对话
        success, code, error_msg = integration.analyze_data_with_context(
            instruction=instruction,
            use_metadata=False
        )
        
        if not success:
            print(f"❌ 第{round_num}轮对话失败: {error_msg}")
            total_checks += 4
            continue
        
        print(f"✅ 第{round_num}轮对话成功生成代码")
        
        # 检查代码质量
        round_checks = 0
        round_total = 4
        
        # 检查1: 不包含失败标记
        if '代码生成失败' not in code:
            print(f"  ✅ 检查1通过: 代码未包含失败标记")
            round_checks += 1
        else:
            print(f"  ❌ 检查1失败: 代码包含失败标记")
        
        # 检查2: 不包含危险操作
        if 'import os' not in code and 'os.makedirs' not in code and 'os.path.join' not in code:
            print(f"  ✅ 检查2通过: 代码不包含危险操作")
            round_checks += 1
        else:
            print(f"  ❌ 检查2失败: 代码仍包含危险操作")
        
        # 检查3: 包含预期关键词
        if any(keyword.lower() in code.lower() for keyword in expected_keywords):
            print(f"  ✅ 检查3通过: 代码包含预期关键词")
            round_checks += 1
        else:
            print(f"  ⚠️ 检查3警告: 代码可能没有包含预期关键词")
        
        # 检查4: 代码执行测试
        exec_success, exec_error = integration.execute_code(code)
        if exec_success:
            print(f"  ✅ 检查4通过: 代码执行成功")
            round_checks += 1
        else:
            print(f"  ❌ 检查4失败: 代码执行失败 - {exec_error}")
        
        print(f"  📊 第{round_num}轮检查结果: {round_checks}/{round_total}")
        
        passed_checks += round_checks
        total_checks += round_total
        
        # 添加对话到历史
        integration.add_conversation_round(
            user_message=instruction,
            assistant_message=f"第{round_num}轮分析完成",
            code=code,
            execution_result={"success": exec_success}
        )
    
    print(f"\n📊 总体检查结果: {passed_checks}/{total_checks} 通过")
    success_rate = passed_checks / total_checks if total_checks > 0 else 0
    
    return success_rate >= 0.8  # 80%通过率算成功

if __name__ == "__main__":
    print("🔧 开始最终完整修复测试...")
    print("=" * 60)
    
    success = test_complete_conversation_flow()
    
    print("=" * 60)
    if success:
        print("🎉 最终完整测试通过！")
        print("✨ 所有问题已完全解决，系统运行正常。")
        print("🚀 用户现在可以正常进行多轮数据分析对话了。")
    else:
        print("❌ 最终完整测试失败，仍需进一步调试。")
