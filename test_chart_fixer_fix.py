#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图表修复器修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.processors.chart_fixer import ChartFixer

def test_chart_fixer():
    """测试图表修复器"""
    print("🧪 测试图表修复器修复...")
    
    # 创建图表修复器
    fixer = ChartFixer()
    
    # 测试包含plotly的代码
    test_code = """
import pandas as pd
import plotly.express as px

result = df.groupby('product')['sales'].sum().reset_index()
fig = px.pie(result, values='sales', names='product', title='产品销售占比')
st.plotly_chart(fig, use_container_width=True)
"""
    
    print("原始代码:")
    print("-" * 40)
    print(test_code)
    print("-" * 40)
    
    # 修复代码
    fixed_code = fixer.fix_charts(test_code, "生成饼图")
    
    print("\n修复后的代码:")
    print("-" * 40)
    print(fixed_code)
    print("-" * 40)
    
    # 检查结果
    success = True
    
    # 检查是否包含危险的字符串操作
    if '"""''' in fixed_code:
        print("❌ 失败: 仍然包含有问题的字符串操作")
        success = False
    else:
        print("✅ 成功: 已移除有问题的字符串操作")
    
    # 检查是否正确导入了plotly
    if 'import plotly.express as px' in fixed_code:
        print("✅ 成功: 正确导入了plotly")
    else:
        print("❌ 失败: 未正确导入plotly")
        success = False
    
    # 检查是否保留了原始功能
    if 'px.pie' in fixed_code:
        print("✅ 成功: 保留了饼图功能")
    else:
        print("❌ 失败: 丢失了饼图功能")
        success = False
    
    return success

def test_code_execution():
    """测试代码执行"""
    print("\n🧪 测试代码执行...")
    
    from core.processors.chart_fixer import ChartFixer
    import pandas as pd
    
    # 创建测试数据
    df = pd.DataFrame({
        'product': ['产品A', '产品B', '产品C'],
        'sales': [100, 200, 150]
    })
    
    # 测试代码
    test_code = """
import pandas as pd
import plotly.express as px

result = df.groupby('product')['sales'].sum().reset_index()
fig = px.pie(result, values='sales', names='product', title='产品销售占比')
print("饼图创建成功")
"""
    
    fixer = ChartFixer()
    fixed_code = fixer.fix_charts(test_code, "生成饼图")
    
    try:
        # 准备执行环境
        import streamlit as st
        import numpy as np

        exec_globals = {
            'df': df,
            'pd': pd,
            'st': st,
            'np': np,
        }
        
        # 执行代码
        exec(fixed_code, exec_globals)
        print("✅ 代码执行成功")
        return True
    except Exception as e:
        print(f"❌ 代码执行失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 开始测试图表修复器修复...")
    
    success1 = test_chart_fixer()
    success2 = test_code_execution()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！图表修复器修复成功。")
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
