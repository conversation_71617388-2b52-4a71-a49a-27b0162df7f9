#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代码清理器修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.processors.code_cleaner import CodeCleaner

def test_dangerous_operations_removal():
    """测试危险操作移除功能"""
    print("🧪 测试危险操作移除功能...")
    
    # 包含危险操作的测试代码
    test_code = """
import pandas as pd
import os
import matplotlib.pyplot as plt

# 分析数据
df = pd.read_csv('data.csv')
result = df.groupby('category')['value'].sum()

# 创建饼图
plt.figure(figsize=(10, 8))
plt.pie(result.values, labels=result.index, autopct='%1.1f%%')
plt.title('各类别占比')
plt.show()
"""
    
    print("原始代码:")
    print("-" * 40)
    print(test_code)
    print("-" * 40)
    
    # 创建代码清理器
    cleaner = CodeCleaner()
    
    # 清理代码
    cleaned_code = cleaner.clean(test_code)
    
    print("\n清理后的代码:")
    print("-" * 40)
    print(cleaned_code)
    print("-" * 40)
    
    # 检查结果
    success = True
    if 'import os' in cleaned_code:
        print("❌ 失败: 仍然包含 'import os'")
        success = False
    else:
        print("✅ 成功: 已移除 'import os'")
    
    if '代码生成失败' in cleaned_code:
        print("❌ 失败: 代码被标记为生成失败")
        success = False
    else:
        print("✅ 成功: 代码未被标记为失败")
    
    if 'plt.pie' in cleaned_code:
        print("✅ 成功: 保留了正常的代码")
    else:
        print("❌ 失败: 丢失了正常的代码")
        success = False
    
    return success

def test_code_validation():
    """测试代码验证功能"""
    print("\n🧪 测试代码验证功能...")
    
    from core.utils.validators import validate_code
    
    # 测试包含危险操作的代码
    dangerous_code = """
import pandas as pd
import os
df = pd.read_csv('test.csv')
"""
    
    is_valid, error_msg, warnings = validate_code(dangerous_code)
    print(f"危险代码验证结果: valid={is_valid}, error='{error_msg}'")
    
    # 测试安全的代码
    safe_code = """
import pandas as pd
import matplotlib.pyplot as plt
df = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})
plt.pie(df['a'], labels=df.index)
"""
    
    is_valid, error_msg, warnings = validate_code(safe_code)
    print(f"安全代码验证结果: valid={is_valid}, error='{error_msg}'")
    
    return True

if __name__ == "__main__":
    print("🔧 开始测试代码清理器修复...")
    
    success1 = test_dangerous_operations_removal()
    success2 = test_code_validation()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！代码清理器修复成功。")
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
