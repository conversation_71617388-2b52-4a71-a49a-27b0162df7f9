#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的第三轮对话修复测试
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.integrations.streamlit_integration import StreamlitLLMIntegration

def test_complete_third_round():
    """完整测试第三轮对话修复"""
    print("🧪 完整测试第三轮对话修复...")
    
    # 创建集成实例
    integration = StreamlitLLMIntegration()
    
    # 自动初始化LLM
    success = integration.auto_initialize_llm()
    if not success:
        print("❌ LLM初始化失败")
        return False
    
    print("✅ LLM初始化成功")
    
    # 加载测试数据
    test_data = pd.DataFrame({
        'region': ['北京', '上海', '广州', '深圳', '杭州'],
        'product': ['产品A', '产品B', '产品A', '产品C', '产品B'],
        'sales': [100, 200, 150, 300, 250],
        'quantity': [10, 20, 15, 30, 25]
    })
    
    success, error_msg = integration.load_data(test_data, "test_data")
    if not success:
        print(f"❌ 数据加载失败: {error_msg}")
        return False
    
    print("✅ 数据加载成功")
    
    # 模拟前两轮对话
    print("📝 添加前两轮对话...")
    
    # 第一轮
    integration.add_conversation_round(
        user_message="分析各地区销售情况",
        assistant_message="我已经分析了各地区的销售情况。",
        code="import pandas as pd\nimport matplotlib.pyplot as plt\nresult = df.groupby('region')['sales'].sum()\nplt.bar(result.index, result.values)\nst.pyplot(plt)",
        execution_result={"success": True}
    )
    
    # 第二轮
    integration.add_conversation_round(
        user_message="显示产品销售排名",
        assistant_message="我已经生成了产品销售排名。",
        code="result = df.groupby('product')['sales'].sum().sort_values(ascending=False)\nst.bar_chart(result)",
        execution_result={"success": True}
    )
    
    print("✅ 前两轮对话已添加")
    
    # 第三轮对话 - 关键测试
    print("🎯 开始第三轮对话测试...")
    
    instruction = "请将上图修改为饼图"
    
    # 使用上下文感知的分析方法
    success, code, error_msg = integration.analyze_data_with_context(
        instruction=instruction,
        use_metadata=False
    )
    
    if not success:
        print(f"❌ 第三轮对话失败: {error_msg}")
        return False
    
    print("✅ 第三轮对话成功生成代码")
    print("生成的代码:")
    print("-" * 60)
    print(code)
    print("-" * 60)
    
    # 检查代码质量
    checks_passed = 0
    total_checks = 5
    
    # 检查1: 不包含失败标记
    if '代码生成失败' not in code:
        print("✅ 检查1通过: 代码未包含失败标记")
        checks_passed += 1
    else:
        print("❌ 检查1失败: 代码包含失败标记")
    
    # 检查2: 不包含危险操作
    if 'import os' not in code:
        print("✅ 检查2通过: 代码不包含危险操作")
        checks_passed += 1
    else:
        print("❌ 检查2失败: 代码仍包含危险操作")
    
    # 检查3: 不包含有问题的字符串操作
    if '"""''' not in code:
        print("✅ 检查3通过: 代码不包含有问题的字符串操作")
        checks_passed += 1
    else:
        print("❌ 检查3失败: 代码包含有问题的字符串操作")
    
    # 检查4: 正确响应饼图请求
    if 'pie' in code.lower() or 'px.pie' in code:
        print("✅ 检查4通过: 代码正确响应了饼图请求")
        checks_passed += 1
    else:
        print("⚠️ 检查4警告: 代码可能没有正确响应饼图请求")
    
    # 检查5: 代码执行测试
    print("🔧 检查5: 测试代码执行...")
    exec_success, exec_error = integration.execute_code(code)
    
    if exec_success:
        print("✅ 检查5通过: 代码执行成功")
        checks_passed += 1
    else:
        print(f"❌ 检查5失败: 代码执行失败 - {exec_error}")
    
    print(f"\n📊 检查结果: {checks_passed}/{total_checks} 通过")
    
    return checks_passed >= 4  # 至少4个检查通过才算成功

if __name__ == "__main__":
    print("🔧 开始完整的第三轮对话修复测试...")
    print("=" * 60)
    
    success = test_complete_third_round()
    
    print("=" * 60)
    if success:
        print("🎉 完整测试通过！第三轮对话问题已完全解决。")
        print("✨ 用户现在可以正常进行多轮对话了。")
    else:
        print("❌ 完整测试失败，仍需进一步调试。")
