#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 dotenv 加载
"""

import os
from pathlib import Path
from dotenv import load_dotenv

def test_dotenv_loading():
    """测试不同路径的 dotenv 加载"""
    print("🧪 测试 dotenv 加载...")
    
    # 清除现有环境变量
    if 'ENABLE_METADATA' in os.environ:
        del os.environ['ENABLE_METADATA']
    
    # 测试1: 加载 config/.env
    print("\n1. 测试加载 config/.env")
    env_path1 = Path("config/.env")
    print(f"   文件存在: {env_path1.exists()}")
    if env_path1.exists():
        result1 = load_dotenv(env_path1)
        print(f"   加载结果: {result1}")
        value1 = os.getenv('ENABLE_METADATA')
        print(f"   ENABLE_METADATA: {value1}")
    
    # 清除环境变量
    if 'ENABLE_METADATA' in os.environ:
        del os.environ['ENABLE_METADATA']
    
    # 测试2: 加载根目录 .env（如果存在）
    print("\n2. 测试加载根目录 .env")
    env_path2 = Path(".env")
    print(f"   文件存在: {env_path2.exists()}")
    if env_path2.exists():
        result2 = load_dotenv(env_path2)
        print(f"   加载结果: {result2}")
        value2 = os.getenv('ENABLE_METADATA')
        print(f"   ENABLE_METADATA: {value2}")
    
    # 测试3: 手动设置环境变量
    print("\n3. 测试手动设置环境变量")
    os.environ['ENABLE_METADATA'] = 'true'
    value3 = os.getenv('ENABLE_METADATA')
    print(f"   ENABLE_METADATA: {value3}")
    
    # 测试4: 读取文件内容
    print("\n4. 直接读取文件内容")
    try:
        with open("config/.env", 'r', encoding='utf-8') as f:
            content = f.read()
            print("   文件内容片段:")
            for line in content.split('\n'):
                if 'ENABLE_METADATA' in line:
                    print(f"     {line}")
    except Exception as e:
        print(f"   读取失败: {e}")

if __name__ == "__main__":
    test_dotenv_loading()
