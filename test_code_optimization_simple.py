#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的代码优化测试
直接测试优化方法的效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_optimization_method_directly():
    """直接测试优化方法"""
    print("🧪 直接测试代码优化方法")
    print("=" * 60)
    
    # 创建一个简化的测试类
    class TestOptimizer:
        def _count_display_lines(self, lines: list) -> int:
            """计算展示代码的行数"""
            display_keywords = [
                'st.subheader', 'st.write', 'st.success', 'st.info', 'st.warning',
                'st.dataframe', 'st.table', 'st.bar_chart', 'st.line_chart',
                'plt.', 'fig.', '.show()', 'print('
            ]
            
            count = 0
            for line in lines:
                if any(keyword in line for keyword in display_keywords):
                    count += 1
            return count
        
        def _optimize_code_for_context(self, code: str) -> str:
            """
            优化代码用于上下文传递
            保留核心逻辑，移除冗余的展示代码
            """
            lines = code.split('\n')
            optimized_lines = []
            
            # 保留的核心逻辑关键词
            core_keywords = [
                'df.groupby', '=', 'sum()', 'mean()', 'count()', 'max()', 'min()',
                'sort_values', 'index[', 'iloc[', 'filter', 'query', 'merge',
                'import ', 'from ', 'def ', 'class ', 'if ', 'for ', 'while '
            ]
            
            # 需要简化的展示代码关键词
            display_keywords = [
                'st.subheader', 'st.write', 'st.success', 'st.info', 'st.warning',
                'st.dataframe', 'st.table', 'st.bar_chart', 'st.line_chart',
                'plt.', 'fig.', '.show()', 'print('
            ]
            
            skipped_display_lines = 0
            
            for line in lines:
                line_stripped = line.strip()
                
                # 跳过空行和注释
                if not line_stripped or line_stripped.startswith('#'):
                    continue
                
                # 检查是否是核心逻辑
                is_core_logic = any(keyword in line for keyword in core_keywords)
                is_display_code = any(keyword in line for keyword in display_keywords)
                
                if is_core_logic:
                    optimized_lines.append(line)
                elif is_display_code:
                    skipped_display_lines += 1
                    # 只保留第一个展示语句作为示例
                    if skipped_display_lines == 1:
                        optimized_lines.append(f"# ... 展示逻辑 (共{self._count_display_lines(lines)}行)")
                else:
                    # 其他逻辑也保留
                    optimized_lines.append(line)
            
            # 如果优化后的代码太短，返回原代码的前10行
            if len(optimized_lines) < 3:
                return '\n'.join(lines[:10]) + ('\n# ... (代码已截断)' if len(lines) > 10 else '')
            
            return '\n'.join(optimized_lines)
    
    # 创建测试实例
    optimizer = TestOptimizer()
    
    # 测试长代码优化
    test_long_code = '''
import pandas as pd
import streamlit as st

# 计算各产品销售额
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)

# 显示标题
st.subheader("📊 各产品销售额分析")

# 显示条形图
st.bar_chart(product_sales)

# 显示数据表
st.dataframe(product_sales.reset_index())

# 找出最佳产品
best_product = product_sales.index[0]
best_amount = product_sales.iloc[0]

# 显示结果
st.success(f"销售额最高的产品: {best_product} (¥{best_amount:,})")

# 计算总销售额
total_sales = product_sales.sum()

# 显示总销售额
st.write(f"总销售额: ¥{total_sales:,}")

# 计算占比
best_ratio = (best_amount / total_sales * 100).round(2)

# 显示占比
st.info(f"{best_product} 占总销售额的 {best_ratio}%")
    '''
    
    print(f"📊 原始代码统计:")
    print(f"  字符数: {len(test_long_code)}")
    print(f"  行数: {len(test_long_code.split(chr(10)))}")
    print(f"  预估Token: {len(test_long_code) // 4}")
    
    # 执行优化
    optimized_code = optimizer._optimize_code_for_context(test_long_code)
    
    print(f"\n📈 优化后代码统计:")
    print(f"  字符数: {len(optimized_code)}")
    print(f"  行数: {len(optimized_code.split(chr(10)))}")
    print(f"  预估Token: {len(optimized_code) // 4}")
    
    # 计算优化效果
    char_reduction = (len(test_long_code) - len(optimized_code)) / len(test_long_code) * 100
    token_reduction = ((len(test_long_code) // 4) - (len(optimized_code) // 4)) / (len(test_long_code) // 4) * 100
    
    print(f"\n🎯 优化效果:")
    print(f"  字符减少: {char_reduction:.1f}%")
    print(f"  Token减少: {token_reduction:.1f}%")
    
    print(f"\n📝 优化后的代码:")
    print("-" * 50)
    print(optimized_code)
    print("-" * 50)
    
    # 测试短代码（应该保持原样）
    test_short_code = '''
import pandas as pd
result = df.groupby('产品')['销售额'].sum()
print(result)
    '''
    
    print(f"\n🧪 测试短代码处理:")
    print(f"原始代码长度: {len(test_short_code)} 字符")
    
    optimized_short = optimizer._optimize_code_for_context(test_short_code)
    print(f"优化后长度: {len(optimized_short)} 字符")
    
    print(f"优化后的短代码:")
    print(optimized_short)
    
    # 验证效果
    if token_reduction >= 20:
        print(f"\n✅ 优化效果达到预期（Token减少{token_reduction:.1f}% ≥ 20%）")
        return True
    else:
        print(f"\n⚠️ 优化效果低于预期（Token减少{token_reduction:.1f}% < 20%）")
        return False

def test_real_scenario():
    """测试真实场景的优化效果"""
    print(f"\n🔗 测试真实场景优化效果")
    print("=" * 60)

    # 创建优化器实例
    class TestOptimizer:
        def _count_display_lines(self, lines: list) -> int:
            """计算展示代码的行数"""
            display_keywords = [
                'st.subheader', 'st.write', 'st.success', 'st.info', 'st.warning',
                'st.dataframe', 'st.table', 'st.bar_chart', 'st.line_chart',
                'plt.', 'fig.', '.show()', 'print('
            ]

            count = 0
            for line in lines:
                if any(keyword in line for keyword in display_keywords):
                    count += 1
            return count

        def _optimize_code_for_context(self, code: str) -> str:
            """优化代码用于上下文传递"""
            lines = code.split('\n')
            optimized_lines = []

            # 保留的核心逻辑关键词
            core_keywords = [
                'df.groupby', '=', 'sum()', 'mean()', 'count()', 'max()', 'min()',
                'sort_values', 'index[', 'iloc[', 'filter', 'query', 'merge',
                'import ', 'from ', 'def ', 'class ', 'if ', 'for ', 'while '
            ]

            # 需要简化的展示代码关键词
            display_keywords = [
                'st.subheader', 'st.write', 'st.success', 'st.info', 'st.warning',
                'st.dataframe', 'st.table', 'st.bar_chart', 'st.line_chart',
                'plt.', 'fig.', '.show()', 'print('
            ]

            skipped_display_lines = 0

            for line in lines:
                line_stripped = line.strip()

                # 跳过空行和注释
                if not line_stripped or line_stripped.startswith('#'):
                    continue

                # 检查是否是核心逻辑
                is_core_logic = any(keyword in line for keyword in core_keywords)
                is_display_code = any(keyword in line for keyword in display_keywords)

                if is_core_logic:
                    optimized_lines.append(line)
                elif is_display_code:
                    skipped_display_lines += 1
                    # 只保留第一个展示语句作为示例
                    if skipped_display_lines == 1:
                        optimized_lines.append(f"# ... 展示逻辑 (共{self._count_display_lines(lines)}行)")
                else:
                    # 其他逻辑也保留
                    optimized_lines.append(line)

            # 如果优化后的代码太短，返回原代码的前10行
            if len(optimized_lines) < 3:
                return '\n'.join(lines[:10]) + ('\n# ... (代码已截断)' if len(lines) > 10 else '')

            return '\n'.join(optimized_lines)

    # 模拟真实的多轮对话场景
    scenarios = [
        {
            'name': '产品销售分析',
            'code': '''
import pandas as pd
import streamlit as st
import matplotlib.pyplot as plt

# 数据分析
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)

# 创建图表
st.subheader("📊 产品销售额排行榜")
st.bar_chart(product_sales)

# 显示详细数据
st.subheader("📋 详细数据")
st.dataframe(product_sales.reset_index())

# 统计信息
best_product = product_sales.index[0]
best_amount = product_sales.iloc[0]
total_sales = product_sales.sum()
best_ratio = (best_amount / total_sales * 100).round(2)

# 显示结果
st.success(f"🏆 销售冠军: {best_product}")
st.info(f"💰 销售额: ¥{best_amount:,}")
st.write(f"📊 占比: {best_ratio}%")
st.write(f"📈 总销售额: ¥{total_sales:,}")
            '''
        },
        {
            'name': '地区分布分析',
            'code': '''
import pandas as pd
import streamlit as st

# 基于之前的最佳产品分析地区分布
best_product_data = df[df['产品名称'] == best_product]
region_sales = best_product_data.groupby('地区')['销售额'].sum().sort_values(ascending=False)

# 显示地区分析
st.subheader(f"🌍 {best_product} 地区销售分布")
st.bar_chart(region_sales)
st.dataframe(region_sales.reset_index())

# 地区统计
best_region = region_sales.index[0]
best_region_amount = region_sales.iloc[0]
region_ratio = (best_region_amount / region_sales.sum() * 100).round(2)

# 显示地区结果
st.success(f"🏆 最佳地区: {best_region}")
st.info(f"💰 该地区销售额: ¥{best_region_amount:,}")
st.write(f"📊 占该产品总销售额: {region_ratio}%")
            '''
        }
    ]
    
    optimizer = TestOptimizer()
    
    total_original_chars = 0
    total_optimized_chars = 0
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 场景 {i}: {scenario['name']}")
        print("-" * 40)
        
        original_code = scenario['code']
        optimized_code = optimizer._optimize_code_for_context(original_code)
        
        original_chars = len(original_code)
        optimized_chars = len(optimized_code)
        
        reduction = (original_chars - optimized_chars) / original_chars * 100
        
        print(f"  原始: {original_chars} 字符, {original_chars // 4} tokens")
        print(f"  优化: {optimized_chars} 字符, {optimized_chars // 4} tokens")
        print(f"  减少: {reduction:.1f}%")
        
        total_original_chars += original_chars
        total_optimized_chars += optimized_chars
    
    # 计算总体效果
    total_reduction = (total_original_chars - total_optimized_chars) / total_original_chars * 100
    total_token_reduction = ((total_original_chars // 4) - (total_optimized_chars // 4)) / (total_original_chars // 4) * 100
    
    print(f"\n🎯 总体优化效果:")
    print(f"  总字符减少: {total_reduction:.1f}%")
    print(f"  总Token减少: {total_token_reduction:.1f}%")
    print(f"  原始总Token: {total_original_chars // 4}")
    print(f"  优化后总Token: {total_optimized_chars // 4}")
    print(f"  节省Token: {(total_original_chars - total_optimized_chars) // 4}")
    
    if total_token_reduction >= 20:
        print(f"\n✅ 总体效果达到预期（Token减少{total_token_reduction:.1f}% ≥ 20%）")
        return True
    else:
        print(f"\n⚠️ 总体效果低于预期（Token减少{total_token_reduction:.1f}% < 20%）")
        return False

if __name__ == "__main__":
    print("🚀 简化版代码优化测试")
    print("=" * 80)
    
    # 运行测试
    test1_success = test_optimization_method_directly()
    test2_success = test_real_scenario()
    
    print(f"\n📋 测试结果总结:")
    print("=" * 60)
    print(f"  基础优化测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"  真实场景测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 立即改进方案验证成功！")
        print("✅ 代码优化方法工作正常")
        print("✅ 达到预期的Token减少效果")
        print("✅ 适用于真实的多轮对话场景")
        print("\n💡 建议：现在可以在实际应用中使用这个优化")
    else:
        print(f"\n⚠️ 部分测试未达到预期")
        print("💡 建议：检查优化逻辑或调整参数")
