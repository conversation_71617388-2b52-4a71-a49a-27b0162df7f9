#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通过底层client调用LLM
验证是否能正确通过llm_client.client.call()调用
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_client_call_fix():
    """测试通过底层client调用"""
    print("🔧 测试通过底层client调用LLM")
    print("=" * 60)
    
    try:
        from core.utils.context_manager import ContextManager
        import streamlit as st
        
        # 模拟streamlit session state
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self._state = {}
                
                def __getattr__(self, key):
                    return self._state.get(key)
                
                def __setattr__(self, key, value):
                    if key.startswith('_'):
                        super().__setattr__(key, value)
                    else:
                        self._state[key] = value
                
                def __contains__(self, key):
                    return key in self._state
            
            st.session_state = MockSessionState()
        
        # 模拟LLM响应对象
        class MockLLMResponse:
            def __init__(self, content):
                self.content = content
                self.tokens_used = 100
        
        # 模拟底层client
        class MockTongyiClient:
            def call(self, instruction, context="", **kwargs):
                print(f"✅ 底层client.call()方法成功调用！")
                print(f"   指令长度: {len(instruction)} 字符")
                
                # 智能响应
                if "进一步" in instruction:
                    content = '''
{
    "has_reference": true,
    "confidence": 0.95,
    "reasoning": "用户使用'进一步'明确表示要基于之前的地区销售额分析结果进行扩展分析",
    "reference_type": "continuation"
}
                    '''
                else:
                    content = '''
{
    "has_reference": false,
    "confidence": 0.1,
    "reasoning": "没有发现明确的引用意图",
    "reference_type": "independent"
}
                    '''
                
                return MockLLMResponse(content)
        
        # 模拟EnhancedTongyiLLM
        class MockEnhancedLLM:
            def __init__(self):
                self.client = MockTongyiClient()
        
        # 模拟integration
        class MockIntegration:
            def get_llm_instance(self):
                return MockEnhancedLLM()
        
        # 设置正确的session state
        st.session_state.integration = MockIntegration()
        st.session_state.context_manager = {
            'conversation_rounds': [
                {
                    'user_message': '分析2024年各地区销售额',
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'execution_result': {'success': True}
                }
            ],
            'reference_tracker': {
                'last_analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'success': True,
                    'timestamp': '2025-08-05T18:47:49'
                }
            }
        }
        
        # 测试
        context_manager = ContextManager(enable_logging=True)
        
        print("📝 测试指令: 进一步分析销售员的销售额")
        print("-" * 40)
        
        references = context_manager._detect_references("进一步分析销售员的销售额")
        
        if references:
            print(f"🎉 成功！检测到引用: {list(references.keys())}")
            for ref_type, ref_data in references.items():
                print(f"   • {ref_type}: {ref_data.get('code', '')[:50]}...")
            return True
        else:
            print(f"❌ 失败：未检测到引用")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_architecture():
    """分析架构层次"""
    print(f"\n🏗️ LLM架构层次分析")
    print("=" * 60)
    
    print("📊 当前架构层次:")
    print("-" * 40)
    
    layers = [
        "1. StreamlitIntegration - 应用集成层",
        "2. EnhancedTongyiLLM - 增强功能层",
        "3. TongyiQianwenClient - API客户端层",
        "4. 通义千问API - 底层服务"
    ]
    
    for layer in layers:
        print(f"  {layer}")
    
    print(f"\n🔧 调用路径:")
    print("-" * 40)
    
    call_path = [
        "context_manager._detect_references()",
        "↓",
        "st.session_state.integration.get_llm_instance()",
        "↓", 
        "EnhancedTongyiLLM实例",
        "↓",
        "llm_client.client.call(instruction, context)",
        "↓",
        "TongyiQianwenClient.call()",
        "↓",
        "通义千问API响应"
    ]
    
    for step in call_path:
        print(f"  {step}")
    
    print(f"\n✅ 修复要点:")
    print("-" * 40)
    
    fixes = [
        "🎯 直接调用底层client - llm_client.client.call()",
        "📝 传递正确参数 - instruction和context",
        "📊 获取响应内容 - response.content",
        "🔄 保持错误处理 - 备用检测机制"
    ]
    
    for fix in fixes:
        print(f"  {fix}")

def show_expected_log():
    """显示预期日志"""
    print(f"\n📋 修复后的预期日志")
    print("=" * 60)
    
    print("✅ 第二轮对话应该显示:")
    print("-" * 40)
    
    expected_logs = [
        "🤖 纯LLM意图分析:",
        "   有引用: True",
        "   置信度: 0.95", 
        "   LLM推理: 用户使用'进一步'明确表示要基于之前的地区销售额分析结果进行扩展分析",
        "   最终结果: ['analysis']",
        "🔗 检测到的引用: ['analysis']"
    ]
    
    for log in expected_logs:
        print(f"  {log}")
    
    print(f"\n❌ 而不是:")
    print("-" * 40)
    
    error_logs = [
        "ERROR - LLM意图分析失败: 'EnhancedTongyiLLM' object has no attribute 'call'",
        "🔗 检测到的引用: 无"
    ]
    
    for log in error_logs:
        print(f"  {log}")

if __name__ == "__main__":
    print("🔧 底层client调用修复测试")
    print("=" * 80)
    
    # 分析架构
    analyze_architecture()
    
    # 测试修复
    success = test_client_call_fix()
    
    # 显示预期日志
    show_expected_log()
    
    print(f"\n📊 测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("\n🎉 底层client调用修复成功！")
        print("✅ 现在通过llm_client.client.call()正确调用LLM")
        print("✅ LLM意图分析应该能正常工作")
        print("💡 请重启应用并重新测试您的两轮对话")
    else:
        print("\n⚠️ 仍需进一步调试")
    
    print(f"\n🎯 关键修复:")
    print("通过底层client调用: llm_client.client.call(instruction=prompt, context='')")
    print("这应该能解决EnhancedTongyiLLM方法调用的问题！")
