#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化修复方案
验证"进一步"等常见词汇是否能被正确识别
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_simple_keyword_detection():
    """测试简化的关键词检测"""
    print("🔧 测试简化的关键词检测")
    print("=" * 60)
    
    try:
        from core.utils.context_manager import ContextManager
        import streamlit as st
        
        # 模拟streamlit session state
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self._state = {}
                
                def __getattr__(self, key):
                    return self._state.get(key)
                
                def __setattr__(self, key, value):
                    if key.startswith('_'):
                        super().__setattr__(key, value)
                    else:
                        self._state[key] = value
                
                def __contains__(self, key):
                    return key in self._state
            
            st.session_state = MockSessionState()
        
        # 设置模拟环境
        st.session_state.context_manager = {
            'conversation_rounds': [
                {
                    'user_message': '分析2024年各地区销售额',
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'execution_result': {'success': True}
                }
            ],
            'reference_tracker': {
                'last_analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'success': True,
                    'timestamp': '2025-08-05T17:54:49'
                }
            }
        }
        
        # 创建上下文管理器
        context_manager = ContextManager(enable_logging=True)
        
        # 测试关键指令
        test_cases = [
            "进一步分析销售员的销售额",  # 这是您日志中的实际指令
            "在此基础上，分析各销售员的销售额",
            "然后分析销售员的业绩",
            "接下来看看产品分布",
            "深入分析销售数据"
        ]
        
        print("📋 关键词检测测试结果:")
        print("-" * 40)
        
        success_count = 0
        
        for i, instruction in enumerate(test_cases, 1):
            print(f"\n{i}. 指令: {instruction}")
            
            # 测试引用检测
            references = context_manager._detect_references(instruction)
            
            if references:
                print(f"   ✅ 检测到引用: {list(references.keys())}")
                success_count += 1
            else:
                print(f"   ❌ 未检测到引用")
        
        print(f"\n📊 测试结果:")
        print(f"   成功率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        
        # 特别测试"进一步"
        print(f"\n🎯 特别测试 - '进一步'关键词:")
        instruction = "进一步分析销售员的销售额"
        references = context_manager._detect_references(instruction)
        
        if references:
            print(f"   ✅ '进一步'被正确识别为引用")
            print(f"   引用类型: {list(references.keys())}")
            return True
        else:
            print(f"   ❌ '进一步'仍然无法识别")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_problem():
    """分析问题根源"""
    print(f"\n🔍 问题根源分析")
    print("=" * 60)
    
    print("您的质疑完全正确：")
    print("-" * 40)
    
    problems = [
        "❌ 我的设计过于复杂化 - 试图用LLM解决简单问题",
        "❌ 基础的关键词列表不完整 - 连'进一步'都没有",
        "❌ 备用机制设计不当 - 应该是主要机制",
        "❌ 架构设计有问题 - 把简单问题搞复杂了"
    ]
    
    for problem in problems:
        print(f"  {problem}")
    
    print(f"\n💡 正确的解决方案应该是:")
    print("-" * 40)
    
    solutions = [
        "✅ 直接使用完整的关键词列表",
        "✅ 包含所有常见的引用表达",
        "✅ 简单、可靠、易维护",
        "✅ 不需要复杂的LLM调用"
    ]
    
    for solution in solutions:
        print(f"  {solution}")

if __name__ == "__main__":
    print("🔧 简化修复方案测试")
    print("=" * 80)
    
    # 分析问题
    analyze_problem()
    
    # 测试修复
    success = test_simple_keyword_detection()
    
    print(f"\n📋 总结:")
    print("=" * 60)
    
    if success:
        print("🎉 修复成功！'进一步'等关键词现在能被正确识别")
        print("✅ 问题解决：从复杂化回归到简单有效的方案")
        print("💡 教训：有时候最简单的方案就是最好的方案")
    else:
        print("⚠️ 仍需进一步调试")
        print("💡 建议：检查关键词列表和检测逻辑")
    
    print(f"\n🎯 您的观点完全正确：")
    print("不是大模型能力不足，而是我的设计有明显问题！")
    print("简单的关键词匹配就能解决的问题，不需要过度工程化。")
