#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于LLM的意图分析器
使用大模型直接分析用户意图，而非规则化匹配
"""

import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class IntentAnalysisResult:
    """意图分析结果"""
    has_reference: bool
    reference_type: str  # 'continuation', 'modification', 'comparison', 'independent'
    confidence: float
    referenced_elements: List[str]  # ['analysis', 'chart', 'table', 'variable']
    action_type: str  # 'analyze', 'modify', 'compare', 'extend'
    reasoning: str  # LLM的推理过程
    suggested_variables: List[str]  # 建议复用的变量

class LLMIntentAnalyzer:
    """基于LLM的意图分析器"""
    
    def __init__(self, llm_client):
        self.llm_client = llm_client
        
    def analyze_intent(self, user_instruction: str, conversation_history: List[Dict]) -> IntentAnalysisResult:
        """
        使用LLM分析用户意图
        
        Args:
            user_instruction: 用户指令
            conversation_history: 对话历史
            
        Returns:
            IntentAnalysisResult对象
        """
        # 构建意图分析提示词
        prompt = self._build_intent_analysis_prompt(user_instruction, conversation_history)
        
        # 调用LLM进行意图分析
        try:
            response = self.llm_client.generate(prompt)
            result = self._parse_llm_response(response)
            return result
        except Exception as e:
            # 如果LLM调用失败，返回默认结果
            return IntentAnalysisResult(
                has_reference=False,
                reference_type='independent',
                confidence=0.0,
                referenced_elements=[],
                action_type='analyze',
                reasoning=f"LLM分析失败: {str(e)}",
                suggested_variables=[]
            )
    
    def _build_intent_analysis_prompt(self, user_instruction: str, conversation_history: List[Dict]) -> str:
        """构建意图分析提示词"""
        
        # 提取历史对话信息
        history_summary = self._summarize_conversation_history(conversation_history)
        
        prompt = f"""你是一个专业的对话意图分析助手。请分析用户的指令是否引用了之前的分析结果。

对话历史摘要：
{history_summary}

当前用户指令：
"{user_instruction}"

请分析以下几个方面：

1. 用户是否在引用之前的分析结果？
2. 如果有引用，是什么类型的引用？
   - continuation: 基于之前结果继续分析
   - modification: 修改之前的分析
   - comparison: 与之前结果对比
   - independent: 独立的新分析

3. 用户想要引用哪些元素？
   - analysis: 分析逻辑和数据处理
   - chart: 图表和可视化
   - table: 数据表格
   - variable: 特定变量

4. 用户想要执行什么动作？
   - analyze: 分析数据
   - modify: 修改现有内容
   - compare: 对比分析
   - extend: 扩展分析

5. 建议复用哪些变量？

请以JSON格式返回分析结果：
{{
    "has_reference": true/false,
    "reference_type": "continuation/modification/comparison/independent",
    "confidence": 0.0-1.0,
    "referenced_elements": ["analysis", "chart", "table", "variable"],
    "action_type": "analyze/modify/compare/extend",
    "reasoning": "你的分析推理过程",
    "suggested_variables": ["变量名1", "变量名2"]
}}

重要：
- 仔细分析用户的语言表达，包括隐含的意图
- 考虑对话的上下文和连贯性
- 即使用户没有明确说"基于"、"根据"等词汇，也要判断是否有引用意图
- 置信度要反映你对分析结果的确信程度
"""
        
        return prompt
    
    def _summarize_conversation_history(self, conversation_history: List[Dict]) -> str:
        """总结对话历史"""
        if not conversation_history:
            return "无对话历史"
        
        summary_parts = []
        for i, round_data in enumerate(conversation_history[-3:], 1):  # 最近3轮
            user_msg = round_data.get('user_message', '')
            code = round_data.get('code', '')
            
            # 提取代码中的关键变量
            key_variables = self._extract_variables_from_code(code)
            
            summary_parts.append(f"""
第{i}轮对话：
- 用户问题：{user_msg}
- 生成的关键变量：{', '.join(key_variables) if key_variables else '无'}
- 代码片段：{code[:200]}{'...' if len(code) > 200 else ''}
""")
        
        return '\n'.join(summary_parts)
    
    def _extract_variables_from_code(self, code: str) -> List[str]:
        """从代码中提取变量名"""
        import re
        if not code:
            return []
        
        # 提取赋值语句中的变量名
        variables = re.findall(r'(\w+)\s*=', code)
        return list(set(variables))
    
    def _parse_llm_response(self, response: str) -> IntentAnalysisResult:
        """解析LLM响应"""
        try:
            # 尝试提取JSON部分
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                result_dict = json.loads(json_str)
                
                return IntentAnalysisResult(
                    has_reference=result_dict.get('has_reference', False),
                    reference_type=result_dict.get('reference_type', 'independent'),
                    confidence=float(result_dict.get('confidence', 0.0)),
                    referenced_elements=result_dict.get('referenced_elements', []),
                    action_type=result_dict.get('action_type', 'analyze'),
                    reasoning=result_dict.get('reasoning', ''),
                    suggested_variables=result_dict.get('suggested_variables', [])
                )
            else:
                raise ValueError("无法找到JSON格式的响应")
                
        except Exception as e:
            # 如果解析失败，尝试基于文本内容进行简单判断
            return self._fallback_parse(response, str(e))
    
    def _fallback_parse(self, response: str, error: str) -> IntentAnalysisResult:
        """备用解析方法"""
        response_lower = response.lower()
        
        # 简单的关键词检测作为备用
        has_reference = any(keyword in response_lower for keyword in [
            '引用', '基于', '继续', '扩展', 'reference', 'based', 'continue'
        ])
        
        confidence = 0.5 if has_reference else 0.1
        
        return IntentAnalysisResult(
            has_reference=has_reference,
            reference_type='continuation' if has_reference else 'independent',
            confidence=confidence,
            referenced_elements=['analysis'] if has_reference else [],
            action_type='analyze',
            reasoning=f"备用解析 - 原始错误: {error}",
            suggested_variables=[]
        )

# 集成到现有系统的适配器
class LLMIntentContextManager:
    """集成LLM意图分析的上下文管理器"""
    
    def __init__(self, llm_client, enable_logging: bool = True):
        self.intent_analyzer = LLMIntentAnalyzer(llm_client)
        self.enable_logging = enable_logging
        self.logger = None
        if enable_logging:
            import logging
            self.logger = logging.getLogger(__name__)
    
    def detect_references_with_llm(self, instruction: str, conversation_history: List[Dict], 
                                  reference_tracker: Dict) -> Dict[str, Any]:
        """
        使用LLM检测引用
        
        Args:
            instruction: 用户指令
            conversation_history: 对话历史
            reference_tracker: 引用跟踪器
            
        Returns:
            包含引用信息的字典
        """
        # 使用LLM分析意图
        intent_result = self.intent_analyzer.analyze_intent(instruction, conversation_history)
        
        # 基于意图分析结果构建引用信息
        references = {}
        if intent_result.has_reference and intent_result.confidence >= 0.3:
            references = self._build_references_from_intent(intent_result, reference_tracker)
        
        # 记录分析结果
        if self.logger:
            self.logger.info(f"🤖 LLM意图分析结果:")
            self.logger.info(f"   有引用: {intent_result.has_reference}")
            self.logger.info(f"   引用类型: {intent_result.reference_type}")
            self.logger.info(f"   置信度: {intent_result.confidence:.2f}")
            self.logger.info(f"   推理过程: {intent_result.reasoning[:100]}...")
            self.logger.info(f"   检测到引用: {list(references.keys()) if references else '无'}")
        
        return {
            'references': references,
            'intent_result': intent_result,
            'has_reference': intent_result.has_reference and intent_result.confidence >= 0.3
        }
    
    def _build_references_from_intent(self, intent_result: IntentAnalysisResult, 
                                    reference_tracker: Dict) -> Dict[str, Any]:
        """基于意图分析结果构建引用信息"""
        references = {}
        
        # 根据LLM识别的引用元素类型添加相应的引用
        for element_type in intent_result.referenced_elements:
            tracker_key = f'last_{element_type}'
            if reference_tracker.get(tracker_key):
                references[element_type] = reference_tracker[tracker_key]
        
        # 如果LLM认为有引用但没有找到具体元素，添加通用分析引用
        if not references and intent_result.has_reference and reference_tracker.get('last_analysis'):
            references['analysis'] = reference_tracker['last_analysis']
        
        return references

# 测试LLM意图分析器
def test_llm_intent_analyzer():
    """测试LLM意图分析器"""
    print("🤖 LLM意图分析器测试")
    print("=" * 60)
    
    # 模拟LLM客户端
    class MockLLMClient:
        def generate(self, prompt):
            # 模拟LLM响应
            return '''
基于对话历史分析，用户的指令"在此基础上，分析各销售员的销售额"明确表示要基于之前的地区销售额分析结果进行扩展分析。

{
    "has_reference": true,
    "reference_type": "continuation",
    "confidence": 0.9,
    "referenced_elements": ["analysis", "variable"],
    "action_type": "extend",
    "reasoning": "用户使用'在此基础上'明确表示要基于之前的分析结果，并且要分析销售员数据，这是对地区分析的扩展",
    "suggested_variables": ["region_sales", "df"]
}
            '''
    
    # 创建分析器
    mock_client = MockLLMClient()
    analyzer = LLMIntentAnalyzer(mock_client)
    
    # 模拟对话历史
    conversation_history = [
        {
            'user_message': '分析各地区销售额',
            'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
            'execution_result': {'success': True}
        }
    ]
    
    # 测试意图分析
    instruction = "在此基础上，分析各销售员的销售额"
    result = analyzer.analyze_intent(instruction, conversation_history)
    
    print(f"📝 测试指令: {instruction}")
    print(f"🤖 LLM分析结果:")
    print(f"   有引用: {result.has_reference}")
    print(f"   引用类型: {result.reference_type}")
    print(f"   置信度: {result.confidence}")
    print(f"   引用元素: {result.referenced_elements}")
    print(f"   动作类型: {result.action_type}")
    print(f"   建议变量: {result.suggested_variables}")
    print(f"   推理过程: {result.reasoning[:200]}...")

if __name__ == "__main__":
    test_llm_intent_analyzer()
