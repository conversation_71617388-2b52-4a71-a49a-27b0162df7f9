#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前元数据功能状态
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_current_metadata_functionality():
    """测试当前元数据功能"""
    print("🧪 测试当前元数据功能状态...")
    
    try:
        # 导入必要的模块
        from core.processors.metadata_processor import MetadataProcessor
        from core.integrations.streamlit_integration import StreamlitLLMIntegration
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '销售额': [8500, 6200, 3200, 4500, 9200],
            '地区': ['北京', '上海', '广州', '深圳', '北京'],
            '产品名称': ['笔记本电脑', '台式电脑', '平板电脑', '手机', '笔记本电脑'],
            '销量': [5, 3, 8, 15, 6],
            '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
            '销售员': ['张三', '李四', '王五', '赵六', '张三']
        })
        
        print("✅ 模块导入成功")
        
        # 测试元数据处理器
        processor = MetadataProcessor()
        metadata = processor.extract_dataframe_metadata(test_data, "test_sales")
        
        print("✅ 元数据提取成功")
        print(f"  提取的列数: {len(metadata.get('columns', {}))}")
        
        # 测试提示词增强
        basic_context = f"""数据形状: {test_data.shape}
列名: {list(test_data.columns)}
数据类型:
{test_data.dtypes.to_string()}

前5行数据:
{test_data.head().to_string()}"""
        
        instruction = "分析各地区销售情况"
        
        # 不使用元数据的提示词
        basic_prompt = processor.enhance_prompt(instruction, basic_context, None, "test_sales")
        
        # 使用元数据的提示词
        enhanced_prompt = processor.enhance_prompt(instruction, basic_context, metadata, "test_sales")
        
        print("✅ 提示词增强成功")
        print(f"  基础提示词长度: {len(basic_prompt)} 字符")
        print(f"  增强提示词长度: {len(enhanced_prompt)} 字符")
        print(f"  元数据增加: {len(enhanced_prompt) - len(basic_prompt)} 字符")
        
        # 检查元数据内容
        if '自动提取的元数据' in enhanced_prompt:
            print("✅ 包含元数据标识")
        else:
            print("❌ 缺少元数据标识")
            
        if '销售额.*numeric' in enhanced_prompt or ('销售额' in enhanced_prompt and 'numeric' in enhanced_prompt):
            print("✅ 包含列类型信息")
        else:
            print("❌ 缺少列类型信息")
        
        # 显示元数据部分
        if '自动提取的元数据:' in enhanced_prompt:
            start_idx = enhanced_prompt.find('自动提取的元数据:')
            end_idx = enhanced_prompt.find('用户指令:', start_idx)
            if end_idx == -1:
                end_idx = len(enhanced_prompt)
            metadata_section = enhanced_prompt[start_idx:end_idx]
            
            print("\n📋 元数据部分内容:")
            print("-" * 60)
            print(metadata_section[:500] + ("..." if len(metadata_section) > 500 else ""))
            print("-" * 60)
        
        # 测试集成
        integration = StreamlitLLMIntegration()
        print(f"✅ 集成创建成功")
        print(f"  LLM是否已初始化: {hasattr(integration, 'llm') and integration.llm is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_basic_vs_metadata_prompts():
    """分析基础提示词与元数据提示词的差异"""
    print("\n🔍 分析基础提示词与元数据提示词的差异...")
    
    # 模拟您选中的基础信息
    basic_info = """数据类型:
日期      object
产品名称    object
销售额      int64
销量       int64
地区      object
销售员     object

前5行数据:
           日期   产品名称   销售额  销量  地区 销售员
0  2024-01-01  笔记本电脑  8500   5  北京  张三
1  2024-01-02   台式电脑  6200   3  上海  李四
2  2024-01-03   平板电脑  3200   8  广州  王五
3  2024-01-04     手机  4500  15  深圳  赵六
4  2024-01-05  笔记本电脑  9200   6  北京  张三

数值列统计:
              销售额         销量
count    20.00000  20.000000
mean   3669.00000  14.200000
std    3039.70376   9.644197
min     150.00000   3.000000
25%     875.00000   5.750000
50%    2850.00000  11.000000
75%    6350.00000  20.500000
max    9200.00000  35.000000"""
    
    print(f"📊 基础信息长度: {len(basic_info)} 字符")
    
    # 模拟元数据信息
    metadata_info = """自动提取的元数据:

列信息:
  - 日期: 类型: text, 描述: 交易发生的日期, 业务含义: 时间维度分析的基础
  - 产品名称: 类型: text, 描述: 销售的产品名称, 业务含义: 产品维度分析
  - 销售额: 类型: numeric, 描述: 销售金额, 业务含义: 核心业务指标
  - 销量: 类型: numeric, 描述: 销售数量, 业务含义: 销售规模指标
  - 地区: 类型: text, 描述: 销售地区, 业务含义: 地理维度分析
  - 销售员: 类型: text, 描述: 负责销售的员工, 业务含义: 人员绩效分析

业务元数据:
表格名称: sales_data
表格描述: 销售数据表，包含产品销售的详细记录
业务领域: 销售管理

推荐分析方法:
- 地区销售额对比分析
- 产品销售趋势分析
- 销售员绩效分析
- 时间序列分析"""
    
    print(f"📊 元数据信息长度: {len(metadata_info)} 字符")
    
    # 分析差异
    print(f"\n📈 信息对比:")
    print(f"  基础信息: {len(basic_info)} 字符")
    print(f"  元数据信息: {len(metadata_info)} 字符")
    print(f"  元数据额外提供: {len(metadata_info)} 字符")
    
    print(f"\n💡 元数据的价值:")
    print(f"  ✅ 业务语义理解: 提供列的业务含义")
    print(f"  ✅ 分析建议: 推荐合适的分析方法")
    print(f"  ✅ 上下文信息: 表格描述和业务领域")
    print(f"  ✅ 智能推理: 基于数据特征的智能建议")
    
    return True

if __name__ == "__main__":
    print("🔧 测试当前元数据功能状态...")
    print("=" * 70)
    
    # 测试1: 当前功能状态
    success = test_current_metadata_functionality()
    
    # 测试2: 分析差异
    analyze_basic_vs_metadata_prompts()
    
    print("=" * 70)
    if success:
        print("🎉 元数据功能测试通过！")
        print("\n💭 关于您的问题：")
        print("基础数据信息（数据类型、前5行、统计信息）与元数据信息是互补的：")
        print("  📊 基础信息: 提供数据的结构和统计特征")
        print("  🧠 元数据信息: 提供业务语义和分析建议")
        print("  🔄 两者结合: 为LLM提供最完整的数据理解")
    else:
        print("❌ 元数据功能测试失败")
    
    print("\n🚀 建议: 保留基础信息，它们与元数据信息各有价值！")
