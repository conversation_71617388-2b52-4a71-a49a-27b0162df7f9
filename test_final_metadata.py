#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试元数据功能
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.integrations.streamlit_integration import StreamlitLLMIntegration

def test_metadata_in_analysis():
    """测试元数据在分析中的应用"""
    print("🧪 测试元数据在分析中的应用...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '销售额': [8500, 6200, 3200, 4500, 9200],
        '地区': ['北京', '上海', '广州', '深圳', '北京'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑', '手机', '笔记本电脑'],
        '销量': [5, 3, 8, 15, 6],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
        '销售员': ['张三', '李四', '王五', '赵六', '张三']
    })
    
    # 创建集成实例
    integration = StreamlitLLMIntegration()
    
    # 模拟数据加载
    integration.df = test_data
    integration.current_file_name = "test_sales_data.csv"
    
    # 测试上下文感知分析
    instruction = "分析各地区销售情况，重点关注销售额分布"
    
    print(f"指令: {instruction}")
    print("开始分析...")
    
    try:
        # 调用上下文感知分析
        success, code, message = integration.analyze_data_with_context(instruction, use_metadata=True)
        
        print("✅ 分析完成！")
        print(f"成功: {success}")
        print(f"消息: {message}")

        if success and code:
            print(f"生成的代码长度: {len(code)} 字符")
            print("代码片段:")
            print(code[:300] + "..." if len(code) > 300 else code)
        
        return success
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_metadata_in_prompt():
    """检查提示词中是否包含元数据"""
    print("\n🔍 检查提示词中的元数据...")
    
    try:
        from core.processors.metadata_processor import MetadataProcessor
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '销售额': [8500, 6200, 3200],
            '地区': ['北京', '上海', '广州'],
            '产品名称': ['笔记本电脑', '台式电脑', '平板电脑']
        })
        
        # 创建元数据处理器
        processor = MetadataProcessor()
        
        # 提取元数据
        metadata = processor.extract_dataframe_metadata(test_data, "test_data")
        
        # 构建上下文
        context = f"数据形状: {test_data.shape}\n列名: {list(test_data.columns)}"
        instruction = "分析销售数据"
        
        # 生成增强提示词
        enhanced_prompt = processor.enhance_prompt(instruction, context, metadata, "test_data")
        
        print(f"提示词长度: {len(enhanced_prompt)} 字符")
        
        # 检查关键元数据信息
        metadata_indicators = [
            '自动提取的元数据',
            '列信息',
            '销售额',
            '地区',
            'numeric',
            'text'
        ]

        found_indicators = []
        for indicator in metadata_indicators:
            if indicator in enhanced_prompt:
                found_indicators.append(indicator)
        
        print(f"找到的元数据指标: {len(found_indicators)}/{len(metadata_indicators)}")
        for indicator in found_indicators:
            print(f"  ✅ {indicator}")
        
        # 显示元数据部分
        if '自动提取的元数据:' in enhanced_prompt:
            start = enhanced_prompt.find('自动提取的元数据:')
            end = enhanced_prompt.find('用户指令:', start)
            if end == -1:
                end = len(enhanced_prompt)
            metadata_section = enhanced_prompt[start:end]
            print(f"\n📋 元数据部分预览:")
            print("-" * 50)
            print(metadata_section[:400] + ("..." if len(metadata_section) > 400 else ""))
            print("-" * 50)
        
        return len(found_indicators) >= 3
        
    except Exception as e:
        print(f"❌ 元数据检查失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 最终测试元数据功能...")
    print("=" * 60)
    
    # 测试1: 检查提示词中的元数据
    success1 = check_metadata_in_prompt()
    
    # 测试2: 测试实际分析
    success2 = test_metadata_in_analysis()
    
    print("=" * 60)
    if success1 and success2:
        print("🎉 元数据功能测试完全通过！")
        print("✨ 元数据增强功能已成功实现并正常工作。")
    else:
        print("⚠️ 部分测试未通过：")
        print(f"  提示词元数据: {'✅' if success1 else '❌'}")
        print(f"  实际分析: {'✅' if success2 else '❌'}")
    
    print("\n🚀 现在您可以在Streamlit应用中体验增强的数据分析功能！")
