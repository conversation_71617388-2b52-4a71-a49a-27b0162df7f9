#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的意图识别和上下文传递流程分析
从用户输入到LLM调用的完整链路追踪
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def trace_complete_intent_flow():
    """追踪完整的意图识别和处理流程"""
    print("🔄 完整意图识别流程追踪")
    print("=" * 80)
    
    # 模拟完整的处理流程
    def simulate_second_round_processing():
        """模拟第二轮对话的完整处理流程"""
        
        print("📝 第二轮对话处理流程:")
        print("-" * 50)
        
        # 步骤1: 用户输入
        user_input = "基于刚才的分析，进一步分析销售额最高产品的地区分布"
        print(f"1️⃣ 用户输入: {user_input}")
        
        # 步骤2: 主应用接收输入 (app/main.py)
        print(f"2️⃣ 主应用接收: StreamlitLLMIntegration.analyze_data_with_context()")
        
        # 步骤3: 上下文管理器构建上下文 (context_manager.py)
        print(f"3️⃣ 上下文构建: ContextManager.build_context_for_llm()")
        
        # 模拟上下文构建过程
        mock_context = {
            'step': '3.1',
            'action': '检测引用关键词',
            'details': {
                'detected_keywords': ['基于', '刚才的', '分析'],
                'time_reference': True,
                'reference_type': 'analysis'
            }
        }
        print(f"  3.1 🔍 引用检测: {mock_context['details']}")
        
        mock_context = {
            'step': '3.2',
            'action': '提取历史对话',
            'details': {
                'recent_rounds': 1,
                'available_code': 'product_sales = df.groupby("产品名称")["销售额"].sum()',
                'execution_success': True
            }
        }
        print(f"  3.2 📚 历史提取: {mock_context['details']}")
        
        mock_context = {
            'step': '3.3',
            'action': '构建引用信息',
            'details': {
                'references': {
                    'analysis': {
                        'code': 'product_sales = df.groupby("产品名称")["销售额"].sum()',
                        'success': True
                    }
                }
            }
        }
        print(f"  3.3 🔗 引用构建: {mock_context['details']['references']}")
        
        # 步骤4: LLM工厂构建增强提示词 (llm_factory.py)
        print(f"4️⃣ 提示词增强: LLMFactory._build_contextual_prompt()")
        
        enhanced_prompt_structure = {
            'role_definition': '你是一个专业的数据分析助手...',
            'data_context': '当前数据信息：数据形状: (100, 5)...',
            'conversation_history': '最近的对话历史：第1轮 - 用户：分析各产品的销售额...',
            'reference_info': '引用信息：之前的analysis代码：product_sales = ...',
            'current_instruction': '当前用户问题：基于刚才的分析，进一步分析...',
            'guidance_principles': '重要指导原则：1. 如果用户提到"之前的"...'
        }
        
        for key, value in enhanced_prompt_structure.items():
            print(f"  4.{list(enhanced_prompt_structure.keys()).index(key)+1} 📋 {key}: {value[:50]}...")
        
        # 步骤5: 发送给LLM API
        print(f"5️⃣ LLM调用: TongyiClient._call_api()")
        
        # 模拟LLM理解和生成过程
        llm_understanding = {
            'intent_recognition': {
                'primary_intent': '地区分布分析',
                'reference_intent': '基于之前的产品销售额分析',
                'continuation_type': '深化分析'
            },
            'context_utilization': {
                'reuse_variables': ['product_sales', 'best_product'],
                'extend_analysis': '从产品维度扩展到地区维度',
                'maintain_continuity': True
            },
            'code_generation_strategy': {
                'approach': '复用已有变量，扩展新的分析逻辑',
                'expected_output': '地区分布图表和统计信息'
            }
        }
        
        print(f"  5.1 🧠 LLM理解:")
        for key, value in llm_understanding.items():
            print(f"    • {key}: {value}")
        
        # 步骤6: LLM生成代码
        generated_code = '''
# 基于第一轮分析的结果
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
best_product = product_sales.index[0]  # 复用之前的变量

st.subheader(f"🌍 {best_product} 在各地区的销售分布")

# 筛选最佳产品的数据
best_product_data = df[df['产品名称'] == best_product]
region_sales = best_product_data.groupby('地区')['销售额'].sum().sort_values(ascending=False)

# 显示地区分布
st.bar_chart(region_sales)
st.dataframe(region_sales.reset_index())

# 地区分析
best_region = region_sales.index[0]
st.success(f"{best_product} 在 {best_region} 地区表现最佳")
        '''
        
        print(f"6️⃣ 代码生成: LLM返回增强代码")
        print(f"  📊 生成代码长度: {len(generated_code)} 字符")
        print(f"  🔄 复用变量: product_sales, best_product")
        print(f"  🆕 新增逻辑: 地区分布分析")
        
        # 步骤7: 代码清理和优化
        print(f"7️⃣ 代码处理: CodeCleaner.clean() + ChartFixer.fix_charts()")
        
        # 步骤8: 返回最终代码
        print(f"8️⃣ 返回结果: 清理后的可执行代码")
        
        return generated_code
    
    # 执行模拟流程
    final_code = simulate_second_round_processing()
    
    print(f"\n🎯 关键技术点总结:")
    print("-" * 50)
    
    key_points = {
        '意图识别': '通过引用关键词检测用户的延续意图',
        '上下文提取': '从历史对话中提取相关的代码和变量',
        '引用匹配': '将用户指令与历史内容进行智能匹配',
        '提示词增强': '将上下文信息整合到LLM提示词中',
        'LLM理解': 'LLM基于增强提示词理解用户真实意图',
        '代码生成': '生成既复用历史又扩展新功能的代码',
        '连贯性保证': '确保生成的代码在逻辑上连贯一致'
    }
    
    for i, (point, description) in enumerate(key_points.items(), 1):
        print(f"{i}. {point}: {description}")

def analyze_intent_recognition_core():
    """分析意图识别的核心机制"""
    print(f"\n🎯 意图识别核心机制分析")
    print("=" * 80)
    
    print("💡 核心问题：LLM如何理解用户的真实意图？")
    print("-" * 50)
    
    # 分析LLM理解机制
    understanding_mechanisms = {
        '1. 显式引用识别': {
            'description': 'LLM通过提示词中的引用信息理解用户指向',
            'example': '用户说"基于刚才的分析" → 提示词包含"之前的analysis代码"',
            'llm_understanding': 'LLM知道要基于特定的历史代码进行扩展'
        },
        '2. 上下文语义理解': {
            'description': 'LLM通过对话历史理解分析的逻辑脉络',
            'example': '第1轮：产品分析 → 第2轮：地区分布 → LLM理解这是分析深化',
            'llm_understanding': 'LLM知道这是从产品维度扩展到地区维度的递进分析'
        },
        '3. 变量连续性推理': {
            'description': 'LLM通过历史代码中的变量理解数据流',
            'example': '历史代码定义了product_sales变量 → 新代码可以直接使用',
            'llm_understanding': 'LLM知道可以复用这些变量，无需重新计算'
        },
        '4. 指导原则约束': {
            'description': 'LLM通过明确的指导原则理解应该如何处理引用',
            'example': '提示词明确说明"参考对话历史和引用信息"',
            'llm_understanding': 'LLM知道必须基于历史内容而不是从零开始'
        }
    }
    
    for mechanism, details in understanding_mechanisms.items():
        print(f"\n{mechanism}")
        print(f"  📝 机制: {details['description']}")
        print(f"  🔍 示例: {details['example']}")
        print(f"  🧠 LLM理解: {details['llm_understanding']}")

def demonstrate_without_context_vs_with_context():
    """对比有无上下文的差异"""
    print(f"\n⚖️ 有无上下文对比分析")
    print("=" * 80)
    
    user_instruction = "基于刚才的分析，进一步分析销售额最高产品的地区分布"
    
    print(f"📝 用户指令: {user_instruction}")
    print(f"\n🔄 处理方式对比:")
    print("-" * 50)
    
    # 无上下文处理
    print("❌ 无上下文处理 (传统方式):")
    no_context_prompt = f"""
你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
数据形状: (100, 5)
列名: ['产品名称', '销售额', '地区', '日期', '销量']

用户指令: {user_instruction}

要求:
1. 只返回可执行的Python代码
2. 数据已经加载在变量 df 中
3. 使用pandas进行数据处理
    """
    
    print(f"  📋 提示词长度: {len(no_context_prompt)} 字符")
    print(f"  🤖 LLM可能的理解: 不知道'刚才的分析'指什么，可能重新分析所有产品")
    print(f"  💻 可能生成的代码: 从头开始的完整产品销售额分析 + 地区分布")
    
    # 有上下文处理
    print(f"\n✅ 有上下文处理 (项目方式):")
    with_context_prompt = f"""
你是一个专业的数据分析助手，能够理解对话历史并提供连贯的分析。

当前数据信息：
数据形状: (100, 5)
列名: ['产品名称', '销售额', '地区', '日期', '销量']

最近的对话历史：
第1轮 - 用户：分析各产品的销售额
第1轮 - 生成代码：
```python
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
st.bar_chart(product_sales)
best_product = product_sales.index[0]
```
第1轮 - 代码执行：成功

引用信息：
之前的analysis代码：
```python
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
best_product = product_sales.index[0]
```

当前用户问题：{user_instruction}

重要指导原则：
1. 如果用户提到'之前的'、'刚才的'、'上面的'等词汇，请参考对话历史和引用信息
2. 可以基于之前的分析结果进行进一步分析或修改
3. 确保代码能够独立运行，包含必要的导入语句
4. 保持分析的连贯性和逻辑性
    """
    
    print(f"  📋 提示词长度: {len(with_context_prompt)} 字符")
    print(f"  🤖 LLM理解: 明确知道'刚才的分析'指第1轮的产品销售额分析")
    print(f"  💻 生成的代码: 复用product_sales和best_product变量，专注地区分布分析")
    
    print(f"\n📊 效果对比:")
    comparison = {
        '理解准确性': {'无上下文': '❌ 模糊', '有上下文': '✅ 精确'},
        '代码效率': {'无上下文': '❌ 重复计算', '有上下文': '✅ 复用结果'},
        '逻辑连贯性': {'无上下文': '❌ 割裂', '有上下文': '✅ 连贯'},
        'Token消耗': {'无上下文': f'{len(no_context_prompt)//4} tokens', '有上下文': f'{len(with_context_prompt)//4} tokens'},
        '用户体验': {'无上下文': '❌ 需重复说明', '有上下文': '✅ 自然对话'}
    }
    
    for metric, values in comparison.items():
        print(f"  • {metric}: {values['无上下文']} vs {values['有上下文']}")

if __name__ == "__main__":
    # 执行完整的流程分析
    trace_complete_intent_flow()
    analyze_intent_recognition_core()
    demonstrate_without_context_vs_with_context()
    
    print(f"\n🎉 完整意图识别流程分析完成!")
    print("=" * 80)
