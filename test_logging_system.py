#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志系统
"""

import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.utils.logger import get_app_logger, get_llm_logger

def test_logger_creation():
    """测试日志记录器创建"""
    print("🧪 测试日志记录器创建...")
    
    # 测试应用日志记录器
    app_logger = get_app_logger()
    print(f"应用日志记录器: {app_logger}")
    print(f"日志级别: {app_logger.level}")
    print(f"处理器数量: {len(app_logger.handlers)}")
    
    for i, handler in enumerate(app_logger.handlers):
        print(f"  处理器 {i+1}: {type(handler).__name__}")
        if hasattr(handler, 'baseFilename'):
            print(f"    文件路径: {handler.baseFilename}")
        print(f"    级别: {handler.level}")
    
    # 测试LLM日志记录器
    llm_logger = get_llm_logger()
    print(f"\nLLM日志记录器: {llm_logger}")
    print(f"日志级别: {llm_logger.level}")
    print(f"处理器数量: {len(llm_logger.handlers)}")
    
    for i, handler in enumerate(llm_logger.handlers):
        print(f"  处理器 {i+1}: {type(handler).__name__}")
        if hasattr(handler, 'baseFilename'):
            print(f"    文件路径: {handler.baseFilename}")
        print(f"    级别: {handler.level}")

def test_logging_output():
    """测试日志输出"""
    print("\n🧪 测试日志输出...")
    
    # 获取日志记录器
    app_logger = get_app_logger()
    llm_logger = get_llm_logger()
    
    # 测试不同级别的日志
    print("发送测试日志...")
    app_logger.debug("这是一条DEBUG日志")
    app_logger.info("这是一条INFO日志")
    app_logger.warning("这是一条WARNING日志")
    app_logger.error("这是一条ERROR日志")
    
    llm_logger.info("这是一条LLM日志")
    
    print("日志发送完成")

def check_log_files():
    """检查日志文件"""
    print("\n🧪 检查日志文件...")
    
    log_dir = Path("logs")
    if not log_dir.exists():
        print("❌ 日志目录不存在")
        return
    
    print(f"日志目录: {log_dir.absolute()}")
    
    # 检查所有日志文件
    log_files = list(log_dir.glob("*.log"))
    print(f"找到 {len(log_files)} 个日志文件:")
    
    for log_file in log_files:
        file_size = log_file.stat().st_size
        print(f"  {log_file.name}: {file_size} 字节")
        
        if file_size > 0:
            print(f"    最后几行内容:")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for line in lines[-3:]:
                        print(f"      {line.strip()}")
            except Exception as e:
                print(f"    读取失败: {e}")
        else:
            print(f"    文件为空")

def test_direct_file_logging():
    """直接测试文件日志"""
    print("\n🧪 直接测试文件日志...")
    
    # 创建一个简单的文件日志记录器
    log_file = Path("logs/test_direct.log")
    log_file.parent.mkdir(exist_ok=True)
    
    # 创建日志记录器
    logger = logging.getLogger("test_direct")
    logger.setLevel(logging.INFO)
    
    # 清除现有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    
    # 测试日志
    logger.info("直接文件日志测试 - 这条日志应该写入文件")
    
    # 强制刷新
    file_handler.flush()
    
    # 检查文件
    if log_file.exists():
        file_size = log_file.stat().st_size
        print(f"测试日志文件: {log_file}")
        print(f"文件大小: {file_size} 字节")
        
        if file_size > 0:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"文件内容:\n{content}")
        else:
            print("文件为空")
    else:
        print("测试日志文件不存在")

def check_logging_permissions():
    """检查日志权限"""
    print("\n🧪 检查日志权限...")
    
    log_dir = Path("logs")
    
    # 检查目录权限
    if log_dir.exists():
        print(f"日志目录存在: {log_dir.absolute()}")
        print(f"目录可写: {log_dir.is_dir() and log_dir.stat().st_mode}")
    else:
        print("日志目录不存在，尝试创建...")
        try:
            log_dir.mkdir(exist_ok=True)
            print("✅ 日志目录创建成功")
        except Exception as e:
            print(f"❌ 日志目录创建失败: {e}")
            return
    
    # 测试文件写入权限
    test_file = log_dir / "permission_test.txt"
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("权限测试")
        print("✅ 文件写入权限正常")
        test_file.unlink()  # 删除测试文件
    except Exception as e:
        print(f"❌ 文件写入权限异常: {e}")

if __name__ == "__main__":
    print("🔧 开始诊断日志系统...")
    print("=" * 60)
    
    # 测试1: 日志记录器创建
    test_logger_creation()
    
    # 测试2: 权限检查
    check_logging_permissions()
    
    # 测试3: 直接文件日志
    test_direct_file_logging()
    
    # 测试4: 日志输出
    test_logging_output()
    
    # 测试5: 检查日志文件
    check_log_files()
    
    print("=" * 60)
    print("🎯 诊断完成！")
