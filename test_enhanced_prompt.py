#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的提示词指导原则
验证LLM是否能按照强化的指导原则生成正确的组合分析代码
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_enhanced_prompt_guidance():
    """测试增强的提示词指导"""
    print("🔧 测试增强的提示词指导原则")
    print("=" * 60)
    
    try:
        from core.llm.llm_factory import EnhancedTongyiLLM
        from core.llm.tongyi_client import TongyiQianwenClient
        from core.utils.config import TongyiConfig
        
        # 模拟LLM响应
        class MockLLMResponse:
            def __init__(self, content):
                self.content = content
                self.tokens_used = 500
        
        # 模拟底层client
        class MockTongyiClient:
            def call(self, instruction, context="", **kwargs):
                print(f"📝 LLM收到的提示词长度: {len(instruction)} 字符")
                
                # 检查提示词是否包含强化的指导原则
                if "🚨 强制性指导原则" in instruction:
                    print("✅ 提示词包含强化的指导原则")
                else:
                    print("❌ 提示词缺少强化的指导原则")
                
                if "变量复用" in instruction:
                    print("✅ 提示词包含变量复用指导")
                else:
                    print("❌ 提示词缺少变量复用指导")
                
                if "组合分析" in instruction:
                    print("✅ 提示词包含组合分析指导")
                else:
                    print("❌ 提示词缺少组合分析指导")
                
                # 模拟正确的组合分析代码生成
                if "进一步分析销售员" in instruction and "region_sales" in instruction:
                    content = '''
# 基于地区分析的销售员扩展分析
import pandas as pd
import streamlit as st

# 确保之前的地区分析变量存在
if 'region_sales' not in locals():
    region_sales = df.groupby('地区')['销售额'].sum().reset_index()

st.subheader("🌍 基于地区分析的销售员表现")

# 地区+销售员组合分析
region_salesperson = df.groupby(['地区', '销售员'])['销售额'].sum().reset_index()

# 展示组合结果
st.write("📊 各地区销售员详细表现:")
st.dataframe(region_salesperson)

# 各地区销售员排名
for region in region_sales['地区']:
    region_data = region_salesperson[region_salesperson['地区'] == region]
    if not region_data.empty:
        st.write(f"📍 {region}地区销售员排名:")
        region_chart_data = region_data.set_index('销售员')['销售额']
        st.bar_chart(region_chart_data)

# 总体销售员表现（基于地区权重）
st.write("🏆 销售员总体表现:")
salesperson_total = df.groupby('销售员')['销售额'].sum().sort_values(ascending=False)
st.bar_chart(salesperson_total)
                    '''
                else:
                    content = '''
# 独立的销售员分析（错误示例）
import pandas as pd
import streamlit as st

salesperson_sales = df.groupby('销售员')['销售额'].sum().reset_index()
st.bar_chart(salesperson_sales, x='销售员', y='销售额')
                    '''
                
                return MockLLMResponse(content)
        
        # 创建模拟的EnhancedTongyiLLM
        mock_client = MockTongyiClient()
        
        # 模拟对话上下文
        conversation_context = {
            'recent_rounds': [
                {
                    'user_message': '分析2024年各地区销售额',
                    'code': '''
import pandas as pd
import streamlit as st
region_sales = df.groupby('地区')['销售额'].sum().reset_index()
st.subheader("各地区销售额分析")
st.bar_chart(region_sales, x="地区", y="销售额")
                    ''',
                    'execution_result': {'success': True}
                }
            ],
            'references': {
                'analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'success': True
                }
            }
        }
        
        # 创建EnhancedTongyiLLM实例
        enhanced_llm = EnhancedTongyiLLM(
            client=mock_client,
            enable_logging=True
        )
        
        # 测试增强的提示词
        instruction = "进一步分析销售员的销售情况"
        context = "数据包含地区、销售员、销售额等字段"
        
        print(f"📝 测试指令: {instruction}")
        print("-" * 40)
        
        # 调用增强的分析方法
        result_code = enhanced_llm.analyze_data_with_context(
            instruction=instruction,
            context=context,
            conversation_context=conversation_context,
            metadata=None,
            table_name="sales_data"
        )
        
        print(f"\n📊 生成的代码分析:")
        print("-" * 40)
        
        # 检查生成的代码质量
        checks_passed = 0
        total_checks = 5
        
        if 'region_sales' in result_code:
            print("✅ 检查1通过: 代码复用了region_sales变量")
            checks_passed += 1
        else:
            print("❌ 检查1失败: 代码没有复用region_sales变量")
        
        if "groupby(['地区', '销售员'])" in result_code or "groupby([\"地区\", \"销售员\"])" in result_code:
            print("✅ 检查2通过: 代码实现了地区+销售员组合分析")
            checks_passed += 1
        else:
            print("❌ 检查2失败: 代码没有实现组合分析")
        
        if '基于' in result_code or '地区' in result_code:
            print("✅ 检查3通过: 代码体现了基于地区的扩展分析")
            checks_passed += 1
        else:
            print("❌ 检查3失败: 代码没有体现扩展分析语义")
        
        if 'for region in' in result_code:
            print("✅ 检查4通过: 代码按地区展示销售员表现")
            checks_passed += 1
        else:
            print("❌ 检查4失败: 代码没有按地区展示")
        
        if result_code.count('st.') >= 3:
            print("✅ 检查5通过: 代码包含丰富的可视化展示")
            checks_passed += 1
        else:
            print("❌ 检查5失败: 代码可视化展示不足")
        
        print(f"\n📋 代码质量评估: {checks_passed}/{total_checks} 通过")
        
        return checks_passed >= 4  # 至少4个检查通过才算成功
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_enhancement_summary():
    """显示增强总结"""
    print(f"\n📋 提示词增强总结")
    print("=" * 60)
    
    print("🔧 关键增强点:")
    print("-" * 40)
    
    enhancements = [
        "🚨 强制性标识 - 使用醒目的emoji和'必须严格遵守'",
        "📝 具体示例 - 提供错误和正确的代码示例对比",
        "🎯 明确检查点 - 生成前的自检清单",
        "📊 标准结构 - 提供引用性代码的标准模板",
        "🔄 变量复用 - 明确指出必须复用特定变量",
        "🌍 组合分析 - 强调多维度分组的重要性"
    ]
    
    for enhancement in enhancements:
        print(f"  {enhancement}")
    
    print(f"\n🎯 预期效果:")
    print("-" * 40)
    
    effects = [
        "✅ LLM将严格按照指导原则执行",
        "✅ 自动复用历史变量如region_sales",
        "✅ 生成地区+销售员的组合分析",
        "✅ 实现真正的基于性扩展分析",
        "✅ 提供丰富的多维度可视化"
    ]
    
    for effect in effects:
        print(f"  {effect}")

if __name__ == "__main__":
    print("🔧 增强提示词指导原则测试")
    print("=" * 80)
    
    # 显示增强总结
    show_enhancement_summary()
    
    # 测试增强效果
    success = test_enhanced_prompt_guidance()
    
    print(f"\n📊 测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("\n🎉 提示词增强成功！")
        print("✅ 强化的指导原则应该能让LLM生成正确的组合分析")
        print("💡 请重启应用并重新测试您的两轮对话")
    else:
        print("\n⚠️ 仍需进一步调试")
    
    print(f"\n🎯 关键改进:")
    print("从温和的'注意'改为强制性的'🚨 必须严格遵守'")
    print("这应该能强制LLM按照指导原则生成正确的组合分析代码！")
