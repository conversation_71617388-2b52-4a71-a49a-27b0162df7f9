#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上下文联动机制深度分析脚本
模拟三轮对话的完整上下文传递过程
"""

import sys
from pathlib import Path
import json
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def simulate_context_linkage():
    """模拟完整的上下文联动机制"""
    print("🔗 上下文联动机制深度分析")
    print("=" * 80)
    
    # 模拟session state结构
    mock_session_state = {
        'context_manager': {
            'conversation_rounds': [],
            'current_summary': None,
            'round_counter': 0,
            'last_summary_round': 0,
            'topic_changed': False,
            'reference_tracker': {
                'last_chart': None,
                'last_table': None,
                'last_analysis': None,
                'variables': {}
            }
        }
    }
    
    print("📊 第一次对话 - 初始化和存储")
    print("-" * 50)
    
    # 第一次对话存储的内容
    first_round = {
        'user_message': '分析销售数据，显示各产品的销售额',
        'assistant_message': '我已经分析了各产品的销售额，生成了条形图显示结果。',
        'code': '''
import streamlit as st
import pandas as pd

# 按产品分组计算销售额
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)

st.subheader("📊 各产品销售额分析")
st.bar_chart(product_sales)

# 显示具体数值
st.dataframe(product_sales.reset_index())

# 显示最高销售额产品
max_product = product_sales.index[0]
max_amount = product_sales.iloc[0]
st.success(f"销售额最高的产品: {max_product} (¥{max_amount:,})")
        ''',
        'execution_result': {'success': True, 'error': None},
        'timestamp': datetime.now().isoformat(),
        'importance_level': 'high'
    }
    
    # 存储第一轮对话
    mock_session_state['context_manager']['conversation_rounds'].append(first_round)
    mock_session_state['context_manager']['round_counter'] = 1
    
    # 更新引用跟踪器
    mock_session_state['context_manager']['reference_tracker'].update({
        'last_chart': {
            'code': first_round['code'],
            'timestamp': first_round['timestamp'],
            'success': True,
            'type': 'bar_chart'
        },
        'last_analysis': {
            'topic': '产品销售额分析',
            'variables': ['product_sales', 'max_product', 'max_amount'],
            'timestamp': first_round['timestamp']
        },
        'variables': {
            'product_sales': '按产品分组的销售额数据',
            'max_product': '销售额最高的产品名称',
            'max_amount': '最高销售额数值'
        }
    })
    
    print("✅ 第一次对话存储内容:")
    print(f"  📝 用户消息: {first_round['user_message']}")
    print(f"  🤖 助手回复: {first_round['assistant_message']}")
    print(f"  💻 生成代码: {len(first_round['code'])} 字符")
    print(f"  ✅ 执行状态: {'成功' if first_round['execution_result']['success'] else '失败'}")
    print(f"  🎯 重要级别: {first_round['importance_level']}")
    print(f"  📊 引用跟踪: 图表代码、分析变量已记录")
    
    print(f"\n📈 第二次对话 - 运用之前结果")
    print("-" * 50)
    
    # 第二次对话 - 基于第一次的结果
    second_instruction = "基于刚才的分析，进一步分析销售额最高的产品在各地区的分布情况"
    
    # 构建第二次对话的上下文
    context_for_second = {
        'has_summary': False,  # 还没有摘要
        'summary': None,
        'recent_rounds': [first_round],  # 包含第一轮对话
        'references': {
            '图表': {
                'success': True,
                'code': first_round['code']
            }
        },
        'current_instruction': second_instruction,
        'total_rounds': 1
    }
    
    print("🔍 第二次对话运用的上下文信息:")
    print(f"  📚 历史轮次: {len(context_for_second['recent_rounds'])} 轮")
    print(f"  🔗 检测到引用: {'图表' if context_for_second['references'] else '无'}")
    print(f"  📊 可用变量: {list(mock_session_state['context_manager']['reference_tracker']['variables'].keys())}")
    
    # 第二次对话生成的代码（基于上下文）
    second_round = {
        'user_message': second_instruction,
        'assistant_message': '基于之前的产品销售额分析，我现在分析销售额最高产品的地区分布。',
        'code': '''
# 基于之前的分析，获取销售额最高的产品
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
max_product = product_sales.index[0]

st.subheader(f"🌍 {max_product} 在各地区的销售分布")

# 筛选该产品的数据
max_product_data = df[df['产品名称'] == max_product]

# 按地区分析
region_sales = max_product_data.groupby('地区')['销售额'].sum().sort_values(ascending=False)

# 显示地区分布图表
st.bar_chart(region_sales)

# 显示详细数据
st.dataframe(region_sales.reset_index())

# 显示占比分析
total_sales = region_sales.sum()
region_percentage = (region_sales / total_sales * 100).round(2)

st.subheader("📊 地区销售占比")
for region, percentage in region_percentage.items():
    st.write(f"{region}: {percentage}% (¥{region_sales[region]:,})")
        ''',
        'execution_result': {'success': True, 'error': None},
        'timestamp': datetime.now().isoformat(),
        'importance_level': 'high'
    }
    
    # 存储第二轮对话
    mock_session_state['context_manager']['conversation_rounds'].append(second_round)
    mock_session_state['context_manager']['round_counter'] = 2
    
    # 更新引用跟踪器
    mock_session_state['context_manager']['reference_tracker'].update({
        'last_chart': {
            'code': second_round['code'],
            'timestamp': second_round['timestamp'],
            'success': True,
            'type': 'region_analysis'
        },
        'last_analysis': {
            'topic': '产品地区分布分析',
            'variables': ['max_product_data', 'region_sales', 'region_percentage'],
            'timestamp': second_round['timestamp']
        }
    })
    
    # 更新变量跟踪
    mock_session_state['context_manager']['reference_tracker']['variables'].update({
        'max_product_data': '销售额最高产品的详细数据',
        'region_sales': '该产品在各地区的销售额',
        'region_percentage': '各地区销售占比'
    })
    
    print("✅ 第二次对话存储内容:")
    print(f"  📝 用户消息: {second_round['user_message']}")
    print(f"  🤖 助手回复: {second_round['assistant_message']}")
    print(f"  💻 生成代码: {len(second_round['code'])} 字符")
    print(f"  🔗 上下文运用: 基于第一次分析的 max_product 变量")
    print(f"  📊 新增变量: max_product_data, region_sales, region_percentage")
    
    print(f"\n🧠 第三次对话 - 复合上下文运用")
    print("-" * 50)
    
    # 第三次对话 - 运用前两次的结果
    third_instruction = "对比一下这个产品和其他产品在主要地区的销售表现"
    
    # 构建第三次对话的上下文（包含前两轮）
    context_for_third = {
        'has_summary': False,  # 还没达到摘要触发条件（4轮）
        'summary': None,
        'recent_rounds': [first_round, second_round],  # 最近两轮对话
        'references': {
            '产品分析': {
                'success': True,
                'code': first_round['code']
            },
            '地区分析': {
                'success': True,
                'code': second_round['code']
            }
        },
        'current_instruction': third_instruction,
        'total_rounds': 2
    }
    
    print("🔍 第三次对话运用的上下文信息:")
    print(f"  📚 历史轮次: {len(context_for_third['recent_rounds'])} 轮")
    print(f"  🔗 检测到引用: {list(context_for_third['references'].keys())}")
    print(f"  📊 累积变量: {len(mock_session_state['context_manager']['reference_tracker']['variables'])} 个")
    
    # 第三次对话生成的代码（综合运用前两次的结果）
    third_round = {
        'user_message': third_instruction,
        'assistant_message': '我将综合前面的产品销售分析和地区分布分析，对比各产品在主要地区的表现。',
        'code': '''
# 综合运用前两次分析的结果
# 1. 获取销售额最高的产品（来自第一次分析）
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
max_product = product_sales.index[0]

# 2. 获取主要地区（来自第二次分析）
region_sales = df.groupby('地区')['销售额'].sum().sort_values(ascending=False)
top_regions = region_sales.head(3).index.tolist()

st.subheader("🏆 产品在主要地区的销售对比分析")

# 创建产品-地区交叉分析
product_region_matrix = df.pivot_table(
    values='销售额', 
    index='产品名称', 
    columns='地区', 
    aggfunc='sum', 
    fill_value=0
)

# 只显示主要地区
main_regions_data = product_region_matrix[top_regions]

# 显示热力图风格的数据
st.dataframe(main_regions_data.style.background_gradient(cmap='YlOrRd'))

# 突出显示最高销售额产品
st.subheader(f"🎯 {max_product} vs 其他产品对比")

for region in top_regions:
    st.write(f"**{region}地区:**")
    region_data = main_regions_data[region].sort_values(ascending=False)
    
    col1, col2 = st.columns(2)
    with col1:
        st.metric(f"{max_product}", f"¥{region_data[max_product]:,}")
    with col2:
        second_best = region_data.index[1] if len(region_data) > 1 else "无"
        if second_best != "无":
            st.metric(f"{second_best}", f"¥{region_data[second_best]:,}")
        ''',
        'execution_result': {'success': True, 'error': None},
        'timestamp': datetime.now().isoformat(),
        'importance_level': 'high'
    }
    
    print("✅ 第三次对话的上下文运用:")
    print(f"  🔄 复用变量: product_sales, max_product (来自第一次)")
    print(f"  🔄 复用逻辑: 地区分析思路 (来自第二次)")
    print(f"  🆕 新增分析: 产品-地区交叉对比")
    print(f"  📊 综合展示: 热力图 + 对比指标")
    
    return mock_session_state

def analyze_context_evolution(session_state):
    """分析上下文演进过程"""
    print(f"\n🔄 上下文演进分析")
    print("-" * 50)
    
    rounds = session_state['context_manager']['conversation_rounds']
    tracker = session_state['context_manager']['reference_tracker']
    
    print("📈 对话轮次演进:")
    for i, round_data in enumerate(rounds, 1):
        print(f"  第{i}轮: {round_data['user_message'][:30]}...")
        print(f"    重要级别: {round_data['importance_level']}")
        print(f"    代码长度: {len(round_data.get('code', ''))} 字符")
    
    print(f"\n🔗 引用跟踪器状态:")
    print(f"  最后图表: {tracker['last_chart']['type'] if tracker['last_chart'] else '无'}")
    print(f"  最后分析: {tracker['last_analysis']['topic'] if tracker['last_analysis'] else '无'}")
    print(f"  累积变量: {len(tracker['variables'])} 个")
    
    print(f"\n📊 变量传递链:")
    for var_name, var_desc in tracker['variables'].items():
        print(f"  • {var_name}: {var_desc}")

def show_summary_trigger_simulation():
    """展示摘要触发机制"""
    print(f"\n📝 摘要触发机制模拟")
    print("-" * 50)
    
    print("🎯 摘要触发条件:")
    print("  • 对话轮次 >= 4 轮")
    print("  • 距离上次摘要 >= 4 轮")
    print("  • 上下文长度 >= 8000 字符")
    
    print(f"\n📋 第4轮对话后的摘要内容示例:")
    sample_summary = {
        'summary_text': '用户正在进行销售数据的深度分析，从整体产品销售额开始，深入到最优产品的地区分布，最后进行产品间的地区对比分析。',
        'key_points': [
            '第1轮: 分析各产品销售额，发现最高销售额产品',
            '第2轮: 分析最高销售额产品的地区分布',
            '第3轮: 对比各产品在主要地区的表现'
        ],
        'user_context': '数据分析师，关注销售业绩和地区分布',
        'core_objective': '全面了解产品销售表现和地区差异',
        'key_concerns': ['产品排名', '地区分布', '对比分析'],
        'current_progress': '已完成产品和地区的基础分析，正在进行深度对比',
        'rounds_covered': 3
    }
    
    for key, value in sample_summary.items():
        print(f"  • {key}: {value}")

if __name__ == "__main__":
    # 执行完整的上下文联动分析
    session_state = simulate_context_linkage()
    
    # 分析上下文演进
    analyze_context_evolution(session_state)
    
    # 展示摘要触发机制
    show_summary_trigger_simulation()
    
    print(f"\n🎉 上下文联动机制分析完成!")
    print("=" * 80)
