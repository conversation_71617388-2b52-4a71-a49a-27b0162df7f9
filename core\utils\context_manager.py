#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上下文管理器 - 实现定期摘要法的连续追问功能
"""

import streamlit as st
import json
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass, asdict
from ..utils.logger import get_app_logger


@dataclass
class ConversationRound:
    """单轮对话数据结构"""
    user_message: str
    assistant_message: str
    code: Optional[str] = None
    execution_result: Optional[Dict] = None
    timestamp: str = ""
    importance_level: str = "medium"  # high, medium, low
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


@dataclass
class ConversationSummary:
    """对话摘要数据结构"""
    summary_text: str
    key_points: List[str]
    user_context: str  # 用户身份/场景
    core_objective: str  # 核心目标
    key_concerns: List[str]  # 关键关注点
    current_progress: str  # 当前进展
    created_at: str = ""
    rounds_covered: int = 0
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()


class ContextManager:
    """
    上下文管理器 - 实现定期摘要法
    
    核心功能：
    1. 对话轮次监控
    2. 智能摘要生成
    3. 上下文传递优化
    4. 性能控制
    """
    
    def __init__(self, enable_logging: bool = True):
        """
        初始化上下文管理器
        
        Args:
            enable_logging: 是否启用日志记录
        """
        self.logger = get_app_logger() if enable_logging else None
        
        # 配置参数
        self.summary_trigger_rounds = 4  # 触发摘要的轮次
        self.max_recent_rounds = 3  # 保留的近期对话轮数
        self.max_context_length = 8000  # 最大上下文长度（字符）
        
        # 初始化session state
        self._init_context_state()
    
    def _init_context_state(self):
        """初始化上下文相关的session state"""
        if 'context_manager' not in st.session_state:
            st.session_state.context_manager = {
                'conversation_rounds': [],  # 对话轮次列表
                'current_summary': None,  # 当前摘要
                'round_counter': 0,  # 轮次计数器
                'last_summary_round': 0,  # 上次摘要的轮次
                'topic_changed': False,  # 主题是否发生变化
                'reference_tracker': {  # 引用跟踪器
                    'last_chart': None,
                    'last_table': None,
                    'last_analysis': None,
                    'variables': {}
                }
            }
    
    def add_conversation_round(self, user_message: str, assistant_message: str, 
                             code: Optional[str] = None, 
                             execution_result: Optional[Dict] = None) -> bool:
        """
        添加一轮完整对话
        
        Args:
            user_message: 用户消息
            assistant_message: 助手回复
            code: 生成的代码
            execution_result: 执行结果
            
        Returns:
            是否需要生成摘要
        """
        try:
            # 创建对话轮次对象
            round_obj = ConversationRound(
                user_message=user_message,
                assistant_message=assistant_message,
                code=code,
                execution_result=execution_result,
                importance_level=self._assess_importance(user_message, code)
            )
            
            # 添加到对话历史
            context_state = st.session_state.context_manager
            context_state['conversation_rounds'].append(asdict(round_obj))
            context_state['round_counter'] += 1
            
            # 更新引用跟踪器
            self._update_reference_tracker(code, execution_result)
            
            # 检查是否需要生成摘要
            should_summarize = self._should_generate_summary()
            
            if self.logger:
                self.logger.info(f"添加对话轮次 #{context_state['round_counter']}, 需要摘要: {should_summarize}")
            
            return should_summarize
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"添加对话轮次失败: {str(e)}")
            return False
    
    def _assess_importance(self, user_message: str, code: Optional[str]) -> str:
        """
        评估对话重要性级别
        
        Args:
            user_message: 用户消息
            code: 生成的代码
            
        Returns:
            重要性级别: high, medium, low
        """
        # 高重要性关键词
        high_importance_keywords = [
            '需求', '要求', '必须', '重要', '关键', '核心', '主要',
            '分析', '统计', '计算', '预测', '建模', '可视化'
        ]
        
        # 低重要性关键词
        low_importance_keywords = [
            '谢谢', '好的', '明白', '了解', '知道了', '可以',
            '随便', '都行', '无所谓', '看看'
        ]
        
        message_lower = user_message.lower()
        
        # 检查高重要性
        if any(keyword in message_lower for keyword in high_importance_keywords):
            return "high"
        
        # 检查低重要性
        if any(keyword in message_lower for keyword in low_importance_keywords):
            return "low"
        
        # 如果有代码生成，提升重要性
        if code and len(code.strip()) > 50:
            return "high"
        
        return "medium"
    
    def _should_generate_summary(self) -> bool:
        """
        判断是否应该生成摘要
        
        Returns:
            是否需要生成摘要
        """
        context_state = st.session_state.context_manager
        rounds_since_summary = context_state['round_counter'] - context_state['last_summary_round']
        
        # 基本轮次触发条件
        if rounds_since_summary >= self.summary_trigger_rounds:
            return True
        
        # 主题变化触发条件
        if context_state['topic_changed']:
            return True
        
        # 高重要性对话累积触发条件
        recent_rounds = context_state['conversation_rounds'][-3:]
        high_importance_count = sum(1 for r in recent_rounds if r.get('importance_level') == 'high')
        if high_importance_count >= 2:
            return True
        
        return False
    
    def _update_reference_tracker(self, code: Optional[str], execution_result: Optional[Dict]):
        """
        更新引用跟踪器
        
        Args:
            code: 生成的代码
            execution_result: 执行结果
        """
        if not code:
            return
        
        context_state = st.session_state.context_manager
        tracker = context_state['reference_tracker']
        
        code_lower = code.lower()
        
        # 跟踪图表生成
        if any(keyword in code_lower for keyword in ['chart', 'plot', 'figure', 'graph']):
            tracker['last_chart'] = {
                'code': code,
                'timestamp': datetime.now().isoformat(),
                'success': execution_result.get('success', False) if execution_result else False
            }
        
        # 跟踪表格生成
        if any(keyword in code_lower for keyword in ['dataframe', 'table', 'df']):
            tracker['last_table'] = {
                'code': code,
                'timestamp': datetime.now().isoformat(),
                'success': execution_result.get('success', False) if execution_result else False
            }
        
        # 跟踪分析结果
        if any(keyword in code_lower for keyword in ['groupby', 'agg', 'sum', 'mean', 'count']):
            tracker['last_analysis'] = {
                'code': code,
                'timestamp': datetime.now().isoformat(),
                'success': execution_result.get('success', False) if execution_result else False
            }

    def generate_summary(self) -> Optional[ConversationSummary]:
        """
        生成对话摘要

        Returns:
            生成的摘要对象，失败时返回None
        """
        try:
            context_state = st.session_state.context_manager
            rounds = context_state['conversation_rounds']

            if not rounds:
                return None

            # 获取需要摘要的轮次
            last_summary_round = context_state['last_summary_round']
            rounds_to_summarize = rounds[last_summary_round:]

            if not rounds_to_summarize:
                return None

            # 提取关键信息
            summary_data = self._extract_summary_data(rounds_to_summarize)

            # 生成摘要文本
            summary_text = self._generate_summary_text(summary_data)

            # 创建摘要对象
            summary = ConversationSummary(
                summary_text=summary_text,
                key_points=summary_data['key_points'],
                user_context=summary_data['user_context'],
                core_objective=summary_data['core_objective'],
                key_concerns=summary_data['key_concerns'],
                current_progress=summary_data['current_progress'],
                rounds_covered=len(rounds_to_summarize)
            )

            # 更新状态
            context_state['current_summary'] = asdict(summary)
            context_state['last_summary_round'] = context_state['round_counter']

            # 详细日志输出
            if self.logger:
                self.logger.info(f"生成摘要成功，覆盖 {len(rounds_to_summarize)} 轮对话")
                self.logger.info("=" * 80)
                self.logger.info("📝 生成的对话摘要详情:")
                self.logger.info(f"  用户身份/场景: {summary.user_context}")
                self.logger.info(f"  核心目标: {summary.core_objective}")
                self.logger.info(f"  关键关注点: {', '.join(summary.key_concerns) if summary.key_concerns else '无'}")
                self.logger.info(f"  当前进展: {summary.current_progress}")
                self.logger.info(f"  覆盖轮次: {summary.rounds_covered}")
                self.logger.info(f"  关键点数量: {len(summary.key_points)}")
                self.logger.info(f"  摘要文本: {summary.summary_text}")
                if summary.key_points:
                    self.logger.info("  关键对话点:")
                    for i, point in enumerate(summary.key_points, 1):
                        self.logger.info(f"    {i}. {point}")
                self.logger.info("=" * 80)

            return summary

        except Exception as e:
            if self.logger:
                self.logger.error(f"生成摘要失败: {str(e)}")
            return None

    def _extract_summary_data(self, rounds: List[Dict]) -> Dict[str, Any]:
        """
        从对话轮次中提取摘要数据

        Args:
            rounds: 对话轮次列表

        Returns:
            提取的摘要数据
        """
        # 提取用户消息和关键信息
        user_messages = [r['user_message'] for r in rounds]
        assistant_messages = [r['assistant_message'] for r in rounds]
        codes = [r.get('code', '') for r in rounds if r.get('code')]

        # 分析用户意图和场景
        user_context = self._analyze_user_context(user_messages)
        core_objective = self._extract_core_objective(user_messages)
        key_concerns = self._extract_key_concerns(user_messages)
        current_progress = self._assess_current_progress(rounds)

        # 提取关键点
        key_points = []
        for i, round_data in enumerate(rounds):
            if round_data.get('importance_level') == 'high':
                key_points.append(f"第{i+1}轮: {round_data['user_message'][:50]}...")

        return {
            'user_context': user_context,
            'core_objective': core_objective,
            'key_concerns': key_concerns,
            'current_progress': current_progress,
            'key_points': key_points,
            'total_rounds': len(rounds),
            'codes_generated': len(codes)
        }

    def _analyze_user_context(self, user_messages: List[str]) -> str:
        """分析用户身份和场景"""
        # 合并所有用户消息
        combined_text = ' '.join(user_messages).lower()

        # 场景关键词映射
        context_keywords = {
            '数据分析师': ['分析', '统计', '数据', '趋势', '指标'],
            '业务人员': ['销售', '业绩', '客户', '营收', '利润'],
            '研究人员': ['研究', '调查', '实验', '模型', '预测'],
            '学生': ['学习', '作业', '练习', '理解', '掌握'],
            '管理者': ['管理', '决策', '策略', '规划', '优化']
        }

        # 匹配最相关的用户身份
        max_score = 0
        best_context = "数据分析用户"

        for context, keywords in context_keywords.items():
            score = sum(1 for keyword in keywords if keyword in combined_text)
            if score > max_score:
                max_score = score
                best_context = context

        return best_context

    def _extract_core_objective(self, user_messages: List[str]) -> str:
        """提取核心目标"""
        # 目标关键词
        objective_patterns = {
            '数据可视化': ['图表', '可视化', '展示', '显示', '绘制'],
            '数据分析': ['分析', '统计', '计算', '汇总', '聚合'],
            '趋势预测': ['预测', '趋势', '预估', '预期', '未来'],
            '问题诊断': ['问题', '异常', '错误', '故障', '诊断'],
            '性能优化': ['优化', '改进', '提升', '增强', '改善']
        }

        combined_text = ' '.join(user_messages).lower()

        for objective, keywords in objective_patterns.items():
            if any(keyword in combined_text for keyword in keywords):
                return objective

        return "数据探索和分析"

    def _extract_key_concerns(self, user_messages: List[str]) -> List[str]:
        """提取关键关注点"""
        concerns = []
        combined_text = ' '.join(user_messages).lower()

        concern_keywords = {
            '数据质量': ['质量', '准确', '完整', '清洁', '错误'],
            '性能效率': ['性能', '速度', '效率', '快速', '优化'],
            '结果准确性': ['准确', '正确', '精确', '可靠', '验证'],
            '可视化效果': ['美观', '清晰', '直观', '易懂', '展示'],
            '业务价值': ['价值', '意义', '作用', '帮助', '收益']
        }

        for concern, keywords in concern_keywords.items():
            if any(keyword in combined_text for keyword in keywords):
                concerns.append(concern)

        return concerns[:3]  # 最多返回3个关注点

    def _assess_current_progress(self, rounds: List[Dict]) -> str:
        """评估当前进展"""
        total_rounds = len(rounds)
        successful_codes = sum(1 for r in rounds
                             if r.get('execution_result', {}).get('success', False))

        if total_rounds == 0:
            return "刚开始对话"
        elif successful_codes / total_rounds > 0.8:
            return "进展顺利，大部分分析成功完成"
        elif successful_codes / total_rounds > 0.5:
            return "进展正常，部分分析遇到问题"
        else:
            return "遇到较多困难，需要调整分析方法"

    def _generate_summary_text(self, summary_data: Dict[str, Any]) -> str:
        """生成摘要文本"""
        template = "{user_context}正在进行{core_objective}，{key_concerns_text}，{current_progress}。已完成{total_rounds}轮对话，生成{codes_generated}段代码。"

        # 处理关注点文本
        concerns = summary_data['key_concerns']
        if concerns:
            key_concerns_text = f"重点关注{', '.join(concerns)}"
        else:
            key_concerns_text = "关注数据分析质量"

        return template.format(
            user_context=summary_data['user_context'],
            core_objective=summary_data['core_objective'],
            key_concerns_text=key_concerns_text,
            current_progress=summary_data['current_progress'],
            total_rounds=summary_data['total_rounds'],
            codes_generated=summary_data['codes_generated']
        )

    def build_context_for_llm(self, current_instruction: str) -> Dict[str, Any]:
        """
        为LLM构建上下文信息

        Args:
            current_instruction: 当前用户指令

        Returns:
            构建的上下文信息
        """
        context_state = st.session_state.context_manager

        # 获取当前摘要
        current_summary = context_state.get('current_summary')

        # 获取最近的对话轮次
        recent_rounds = context_state['conversation_rounds'][-self.max_recent_rounds:]

        # 检查引用
        references = self._detect_references(current_instruction)

        # 构建上下文
        context = {
            'has_summary': current_summary is not None,
            'summary': current_summary,
            'recent_rounds': recent_rounds,
            'references': references,
            'current_instruction': current_instruction,
            'total_rounds': context_state['round_counter']
        }

        # 详细日志输出 - 构建的上下文信息
        if self.logger:
            self.logger.info("=" * 80)
            self.logger.info("🧠 为LLM构建的上下文信息:")
            self.logger.info(f"  当前指令: {current_instruction}")
            self.logger.info(f"  总对话轮次: {context['total_rounds']}")
            self.logger.info(f"  是否有摘要: {context['has_summary']}")

            if context['has_summary'] and current_summary:
                self.logger.info("  📝 使用的摘要信息:")
                self.logger.info(f"    摘要文本: {current_summary['summary_text']}")
                self.logger.info(f"    用户身份: {current_summary['user_context']}")
                self.logger.info(f"    核心目标: {current_summary['core_objective']}")
                if current_summary.get('key_concerns'):
                    self.logger.info(f"    关注点: {', '.join(current_summary['key_concerns'])}")

            self.logger.info(f"  📚 最近对话轮次数: {len(recent_rounds)}")
            if recent_rounds:
                self.logger.info("  最近对话内容:")
                for i, round_data in enumerate(recent_rounds, 1):
                    self.logger.info(f"    第{i}轮 - 用户: {round_data['user_message'][:50]}...")
                    if round_data.get('code'):
                        self.logger.info(f"    第{i}轮 - 代码: {round_data['code'][:100]}...")
                    exec_result = round_data.get('execution_result', {})
                    success_status = "成功" if exec_result.get('success') else "失败"
                    self.logger.info(f"    第{i}轮 - 执行: {success_status}")

            self.logger.info(f"  🔗 检测到的引用: {list(references.keys()) if references else '无'}")
            if references:
                for ref_type, ref_data in references.items():
                    if ref_data:
                        success_status = "成功" if ref_data.get('success') else "失败"
                        self.logger.info(f"    {ref_type}: {ref_data.get('code', '')[:50]}... (执行{success_status})")

            self.logger.info("=" * 80)

        return context

    def _detect_references(self, instruction: str) -> Dict[str, Any]:
        """
        使用LLM智能检测用户指令中的引用

        Args:
            instruction: 用户指令

        Returns:
            检测到的引用信息
        """
        context_state = st.session_state.context_manager
        conversation_history = context_state.get('conversation_rounds', [])
        reference_tracker = context_state['reference_tracker']

        # 纯LLM意图分析
        result = self._pure_llm_intent_analysis(instruction, conversation_history, reference_tracker)
        return result.get('references', {})

    def _pure_llm_intent_analysis(self, instruction: str, conversation_history: List[Dict],
                                reference_tracker: Dict) -> Dict[str, Any]:
        """
        纯LLM驱动的意图分析 - 不使用任何关键词匹配
        """
        try:
            # 获取LLM客户端 - 通过integration获取
            llm_client = None
            if hasattr(st.session_state, 'integration'):
                llm_client = st.session_state.integration.get_llm_instance()

            if not llm_client:
                if self.logger:
                    self.logger.warning("LLM不可用，使用简单备用检测")
                return self._simple_fallback_detection(instruction, conversation_history, reference_tracker)

            # 构建LLM提示词
            prompt = self._build_pure_llm_prompt(instruction, conversation_history)

            # 调用LLM
            response = llm_client.generate(prompt)

            # 解析LLM响应
            analysis_result = self._parse_llm_analysis(response)

            # 基于LLM分析结果构建引用信息
            references = {}
            if analysis_result.get('has_reference', False):
                if reference_tracker.get('last_analysis'):
                    references['analysis'] = reference_tracker['last_analysis']

            if self.logger:
                self.logger.info(f"🤖 纯LLM意图分析:")
                self.logger.info(f"   有引用: {analysis_result.get('has_reference', False)}")
                self.logger.info(f"   置信度: {analysis_result.get('confidence', 0):.2f}")
                self.logger.info(f"   LLM推理: {analysis_result.get('reasoning', '')[:100]}...")
                self.logger.info(f"   最终结果: {list(references.keys()) if references else '无引用'}")

            return {'references': references}

        except Exception as e:
            if self.logger:
                self.logger.error(f"LLM意图分析失败: {str(e)}")
            # 没有备用方案，直接返回无引用
            return {'references': {}}

    def _simple_fallback_detection(self, instruction: str, conversation_history: List[Dict],
                                 reference_tracker: Dict) -> Dict[str, Any]:
        """简单但有效的备用检测"""
        instruction_lower = instruction.lower()
        references = {}

        # 关键引用词汇
        reference_keywords = [
            '进一步', '深入', '详细', '扩展', '补充',
            '然后', '接下来', '下一步', '继续',
            '基于', '根据', '在此基础上', '在这基础上',
            '之前', '刚才', '上面', '前面'
        ]

        # 检查是否包含引用关键词
        has_reference_keyword = any(kw in instruction_lower for kw in reference_keywords)

        # 检查是否有对话历史
        has_conversation_history = len(conversation_history) > 0

        # 如果有引用关键词且有对话历史，认为是引用
        if has_reference_keyword and has_conversation_history:
            if reference_tracker.get('last_analysis'):
                references['analysis'] = reference_tracker['last_analysis']
                if self.logger:
                    self.logger.info(f"🔄 备用检测成功: 发现引用关键词，添加analysis引用")

        return {'references': references}

    def _build_pure_llm_prompt(self, instruction: str, conversation_history: List[Dict]) -> str:
        """构建纯LLM意图分析提示词"""

        # 构建对话历史上下文
        history_context = "无对话历史"
        if conversation_history:
            recent_rounds = conversation_history[-2:]  # 最近2轮
            context_parts = []

            for i, round_data in enumerate(recent_rounds, 1):
                user_msg = round_data.get('user_message', '')
                code = round_data.get('code', '')

                # 提取关键信息
                import re
                variables = re.findall(r'(\w+)\s*=', code)
                key_operations = []
                if 'groupby' in code.lower():
                    key_operations.append('数据分组')
                if 'sum()' in code.lower():
                    key_operations.append('求和计算')
                if 'chart' in code.lower():
                    key_operations.append('图表展示')

                context_parts.append(f"""
第{i}轮对话：
- 用户需求：{user_msg}
- 主要操作：{', '.join(key_operations) if key_operations else '数据处理'}
- 生成变量：{', '.join(set(variables)) if variables else '无'}
""")

            history_context = '\n'.join(context_parts)

        prompt = f"""你是一个专业的对话意图分析专家。请分析用户的当前指令是否需要引用之前的分析结果。

对话历史：
{history_context}

当前用户指令："{instruction}"

请深入分析：
1. 用户的指令是否暗示要基于之前的分析结果？
2. 用户是否想要扩展、延续或修改之前的分析？
3. 即使没有明确的引用词汇，是否存在逻辑上的连续性？

分析要点：
- 考虑指令的语义含义，不仅仅是字面意思
- 分析用户的真实意图和需求
- 评估与历史对话的关联程度

请以JSON格式返回分析结果：
{{
    "has_reference": true/false,
    "confidence": 0.0-1.0,
    "reasoning": "详细的分析推理过程",
    "reference_type": "continuation/modification/comparison/independent"
}}

只返回JSON，不要其他内容。"""

        return prompt

    def _parse_llm_analysis(self, response: str) -> Dict[str, Any]:
        """解析LLM分析结果"""
        import json

        try:
            # 提取JSON部分
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)

                return {
                    'has_reference': result.get('has_reference', False),
                    'confidence': float(result.get('confidence', 0.0)),
                    'reasoning': result.get('reasoning', ''),
                    'reference_type': result.get('reference_type', 'independent')
                }
        except Exception as e:
            if self.logger:
                self.logger.warning(f"解析LLM响应失败: {str(e)}")

        # 如果解析失败，返回保守结果
        return {
            'has_reference': False,
            'confidence': 0.0,
            'reasoning': 'LLM响应解析失败',
            'reference_type': 'independent'
        }

    # 代码已简化，只保留纯LLM方法

    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """解析LLM响应"""
        import json
        try:
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                result = json.loads(json_str)
                return {
                    'has_reference': result.get('has_reference', False),
                    'reference_type': result.get('reference_type', 'independent'),
                    'confidence': float(result.get('confidence', 0.0)),
                    'referenced_elements': result.get('referenced_elements', []),
                    'reasoning': result.get('reasoning', '')
                }
        except:
            pass

        # 备用解析
        response_lower = response.lower()
        has_ref = 'true' in response_lower or '引用' in response_lower
        return {
            'has_reference': has_ref,
            'reference_type': 'continuation' if has_ref else 'independent',
            'confidence': 0.6 if has_ref else 0.1,
            'referenced_elements': ['analysis'] if has_ref else [],
            'reasoning': '备用解析'
        }

    def _build_references_from_intent(self, intent_result: Dict, reference_tracker: Dict) -> Dict[str, Any]:
        """基于意图分析构建引用信息"""
        references = {}
        for element_type in intent_result.get('referenced_elements', []):
            tracker_key = f'last_{element_type}'
            if reference_tracker.get(tracker_key):
                references[element_type] = reference_tracker[tracker_key]

        if not references and intent_result.get('has_reference') and reference_tracker.get('last_analysis'):
            references['analysis'] = reference_tracker['last_analysis']

        return references

    def _fallback_detect_references(self, instruction: str, reference_tracker: Dict) -> Dict[str, Any]:
        """备用检测方法"""
        instruction_lower = instruction.lower()
        references = {}

        # 扩展关键词列表，包含更多引用表达
        keywords = [
            '之前', '刚才', '基础上', '基于', '根据', '然后', '接下来', '继续',
            '进一步', '深入', '详细', '扩展', '补充', '另外', '同时', '以及',
            '在此', '在这', '上面', '前面', '刚刚'
        ]

        # 检查是否包含引用关键词
        has_reference_keyword = any(kw in instruction_lower for kw in keywords)

        # 检查是否是相关分析（即使没有明确关键词）
        analysis_related = any(word in instruction_lower for word in ['分析', '销售', '数据', '统计'])

        if has_reference_keyword or analysis_related:
            if reference_tracker.get('last_analysis'):
                references['analysis'] = reference_tracker['last_analysis']
                if self.logger:
                    self.logger.info(f"🔄 备用检测成功: 关键词={has_reference_keyword}, 相关分析={analysis_related}")

        return {'references': references}

    def _build_intent_analysis_prompt(self, user_instruction: str, conversation_history: List[Dict]) -> str:
        """构建意图分析提示词"""

        # 提取历史对话信息
        history_summary = self._summarize_conversation_for_intent(conversation_history)

        prompt = f"""你是一个专业的对话意图分析助手。请分析用户的指令是否引用了之前的分析结果。

对话历史摘要：
{history_summary}

当前用户指令：
"{user_instruction}"

请分析用户是否在引用之前的分析结果，并以JSON格式返回：

{{
    "has_reference": true/false,
    "reference_type": "continuation/modification/comparison/independent",
    "confidence": 0.0-1.0,
    "referenced_elements": ["analysis", "chart", "table"],
    "action_type": "analyze/modify/compare/extend",
    "reasoning": "分析推理过程",
    "suggested_variables": ["变量名1", "变量名2"]
}}

分析要点：
1. 即使用户没有明确说"基于"、"根据"等词汇，也要判断是否有引用意图
2. 考虑对话的上下文和连贯性
3. 置信度要反映分析结果的确信程度
4. 重点关注用户是否想要基于之前的分析结果进行扩展

请只返回JSON，不要其他解释。"""

        return prompt

    # 清理完成，保持代码简洁

    def get_context_stats(self) -> Dict[str, Any]:
        """
        获取上下文统计信息

        Returns:
            上下文统计数据
        """
        context_state = st.session_state.context_manager

        return {
            'total_rounds': context_state['round_counter'],
            'rounds_since_summary': context_state['round_counter'] - context_state['last_summary_round'],
            'has_summary': context_state['current_summary'] is not None,
            'summary_trigger_threshold': self.summary_trigger_rounds,
            'recent_rounds_count': len(context_state['conversation_rounds'][-self.max_recent_rounds:]),
            'memory_usage_estimate': self._estimate_memory_usage()
        }

    def _estimate_memory_usage(self) -> str:
        """估算内存使用量"""
        context_state = st.session_state.context_manager

        # 估算对话轮次占用的内存
        rounds_size = len(str(context_state['conversation_rounds']))

        # 估算摘要占用的内存
        summary_size = len(str(context_state['current_summary'])) if context_state['current_summary'] else 0

        total_size = rounds_size + summary_size

        if total_size < 1024:
            return f"{total_size} bytes"
        elif total_size < 1024 * 1024:
            return f"{total_size / 1024:.1f} KB"
        else:
            return f"{total_size / (1024 * 1024):.1f} MB"

    def cleanup_old_data(self, keep_recent_rounds: int = 5):
        """
        清理旧数据，释放内存

        Args:
            keep_recent_rounds: 保留的最近轮次数
        """
        try:
            context_state = st.session_state.context_manager

            # 保留最近的对话轮次
            if len(context_state['conversation_rounds']) > keep_recent_rounds:
                context_state['conversation_rounds'] = context_state['conversation_rounds'][-keep_recent_rounds:]

                # 更新计数器
                context_state['last_summary_round'] = max(0, context_state['last_summary_round'] -
                                                        (context_state['round_counter'] - keep_recent_rounds))
                context_state['round_counter'] = keep_recent_rounds

            if self.logger:
                self.logger.info(f"清理旧数据完成，保留最近 {keep_recent_rounds} 轮对话")

        except Exception as e:
            if self.logger:
                self.logger.error(f"清理旧数据失败: {str(e)}")

    def reset_context(self):
        """重置上下文状态"""
        try:
            st.session_state.context_manager = {
                'conversation_rounds': [],
                'current_summary': None,
                'round_counter': 0,
                'last_summary_round': 0,
                'topic_changed': False,
                'reference_tracker': {
                    'last_chart': None,
                    'last_table': None,
                    'last_analysis': None,
                    'variables': {}
                }
            }

            if self.logger:
                self.logger.info("上下文状态已重置")

        except Exception as e:
            if self.logger:
                self.logger.error(f"重置上下文状态失败: {str(e)}")

    def export_context_data(self) -> Dict[str, Any]:
        """
        导出上下文数据

        Returns:
            可序列化的上下文数据
        """
        context_state = st.session_state.context_manager
        stats = self.get_context_stats()

        return {
            'export_timestamp': datetime.now().isoformat(),
            'context_state': context_state,
            'stats': stats,
            'config': {
                'summary_trigger_rounds': self.summary_trigger_rounds,
                'max_recent_rounds': self.max_recent_rounds,
                'max_context_length': self.max_context_length
            }
        }
