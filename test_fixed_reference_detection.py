#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的引用检测
验证LLM不可用时的备用检测是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_fixed_reference_detection():
    """测试修复后的引用检测"""
    print("🔧 测试修复后的引用检测")
    print("=" * 60)
    
    try:
        from core.utils.context_manager import ContextManager
        import streamlit as st
        
        # 模拟streamlit session state
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self._state = {}
                
                def __getattr__(self, key):
                    return self._state.get(key)
                
                def __setattr__(self, key, value):
                    if key.startswith('_'):
                        super().__setattr__(key, value)
                    else:
                        self._state[key] = value
                
                def __contains__(self, key):
                    return key in self._state
            
            st.session_state = MockSessionState()
        
        # 设置模拟环境 - 注意：不设置LLM客户端，模拟LLM不可用的情况
        st.session_state.context_manager = {
            'conversation_rounds': [
                {
                    'user_message': '分析2024年各地区销售额',
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'execution_result': {'success': True}
                }
            ],
            'reference_tracker': {
                'last_analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'success': True,
                    'timestamp': '2025-08-05T18:24:12'
                }
            }
        }
        
        # 创建上下文管理器
        context_manager = ContextManager(enable_logging=True)
        
        # 测试您日志中的实际指令
        test_instruction = "进一步分析销售员的销售情况"
        
        print(f"📝 测试指令: {test_instruction}")
        print(f"🔧 模拟场景: LLM不可用，使用备用检测")
        print("-" * 40)
        
        # 测试引用检测
        references = context_manager._detect_references(test_instruction)
        
        print(f"📊 检测结果:")
        if references:
            print(f"   ✅ 检测到引用: {list(references.keys())}")
            for ref_type, ref_data in references.items():
                print(f"      • {ref_type}: {ref_data.get('code', '')[:50]}...")
            return True
        else:
            print(f"   ❌ 未检测到引用")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_context_building():
    """测试上下文构建"""
    print(f"\n🏗️ 测试上下文构建")
    print("=" * 60)
    
    try:
        from core.utils.context_manager import ContextManager
        import streamlit as st
        
        # 确保session state存在
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self._state = {}
                
                def __getattr__(self, key):
                    return self._state.get(key)
                
                def __setattr__(self, key, value):
                    if key.startswith('_'):
                        super().__setattr__(key, value)
                    else:
                        self._state[key] = value
                
                def __contains__(self, key):
                    return key in self._state
            
            st.session_state = MockSessionState()
        
        # 设置完整的对话状态
        st.session_state.context_manager = {
            'conversation_rounds': [
                {
                    'user_message': '分析2024年各地区销售额',
                    'assistant_message': '已完成地区销售额分析',
                    'code': '''
import pandas as pd
import streamlit as st
region_sales = df.groupby('地区')['销售额'].sum().reset_index()
st.subheader("各地区销售额分析")
st.bar_chart(region_sales, x="地区", y="销售额")
                    ''',
                    'execution_result': {'success': True},
                    'timestamp': '2025-08-05T18:24:12'
                }
            ],
            'current_summary': None,
            'round_counter': 1,
            'last_summary_round': 0,
            'topic_changed': False,
            'reference_tracker': {
                'last_analysis': {
                    'code': '''
region_sales = df.groupby('地区')['销售额'].sum().reset_index()
st.subheader("各地区销售额分析")
st.bar_chart(region_sales, x="地区", y="销售额")
                    ''',
                    'success': True,
                    'timestamp': '2025-08-05T18:24:12'
                },
                'variables': {},
                'extracted_variables': {}
            }
        }
        
        context_manager = ContextManager(enable_logging=False)
        
        # 测试第二轮指令的上下文构建
        instruction = "进一步分析销售员的销售情况"
        
        print(f"📝 测试指令: {instruction}")
        print("-" * 40)
        
        # 构建上下文
        context = context_manager.build_context_for_llm(instruction)
        
        print(f"📊 上下文构建结果:")
        print(f"  总轮次: {context['total_rounds']}")
        print(f"  最近轮次: {len(context['recent_rounds'])}")
        print(f"  检测到引用: {list(context['references'].keys()) if context['references'] else '无'}")
        
        if context['references']:
            print(f"  引用详情:")
            for ref_type, ref_data in context['references'].items():
                print(f"    • {ref_type}: {ref_data['code'][:100]}...")
        
        # 检查是否包含关键变量信息
        if context['recent_rounds']:
            recent_code = context['recent_rounds'][0].get('code', '')
            if 'region_sales' in recent_code:
                print(f"  ✅ 包含关键变量 region_sales")
            else:
                print(f"  ❌ 缺少关键变量 region_sales")
        
        # 检查引用信息是否正确传递
        has_analysis_reference = 'analysis' in context.get('references', {})
        print(f"  引用传递: {'✅ 成功' if has_analysis_reference else '❌ 失败'}")
        
        return has_analysis_reference
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_log_issue():
    """分析日志问题"""
    print(f"\n🔍 日志问题分析")
    print("=" * 60)
    
    print("📋 从您的日志发现的问题:")
    print("-" * 40)
    
    issues = [
        "❌ LLM不可用 - 系统无法调用LLM进行意图分析",
        "❌ 引用检测异常 - 显示['references']而不是具体引用内容",
        "❌ 第二轮独立分析 - 没有基于第一轮的地区分析结果",
        "❌ 缺少组合分析 - 没有生成地区+销售员的组合分析"
    ]
    
    for issue in issues:
        print(f"  {issue}")
    
    print(f"\n✅ 修复措施:")
    print("-" * 40)
    
    fixes = [
        "🔧 修复返回格式 - 确保_detect_references返回正确的字典格式",
        "🔄 添加备用检测 - LLM不可用时使用关键词检测",
        "📝 改进关键词列表 - 包含'进一步'等常见引用词汇",
        "🎯 确保引用传递 - 引用信息正确传递给代码生成LLM"
    ]
    
    for fix in fixes:
        print(f"  {fix}")
    
    print(f"\n🎯 预期改进效果:")
    print("-" * 40)
    
    expected = [
        "✅ 第二轮日志将显示: 🔗 检测到的引用: ['analysis']",
        "✅ 引用信息将包含region_sales变量",
        "✅ 代码生成LLM将收到明确的引用指导",
        "✅ 生成地区+销售员的组合分析代码"
    ]
    
    for exp in expected:
        print(f"  {exp}")

if __name__ == "__main__":
    print("🔧 修复后的引用检测测试")
    print("=" * 80)
    
    # 分析日志问题
    analyze_log_issue()
    
    # 测试修复效果
    test1_success = test_fixed_reference_detection()
    test2_success = test_context_building()
    
    print(f"\n📋 测试结果总结:")
    print("=" * 60)
    print(f"  引用检测: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"  上下文构建: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 修复成功！")
        print("✅ 备用检测机制正常工作")
        print("✅ 引用信息正确传递")
        print("✅ 上下文构建完整")
        print("\n💡 建议：重启应用并重新测试相同的对话")
    else:
        print(f"\n⚠️ 部分功能仍需调试")
    
    print(f"\n🎯 关键修复点:")
    print("现在即使LLM不可用，系统也能通过备用检测识别'进一步'等引用意图！")
