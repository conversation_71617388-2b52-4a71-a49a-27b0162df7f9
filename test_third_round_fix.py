#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第三轮对话修复
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.integrations.streamlit_integration import StreamlitLLMIntegration
from core.utils.context_manager import ContextManager

def test_third_round_conversation():
    """测试第三轮对话"""
    print("🧪 测试第三轮对话修复...")
    
    # 创建集成实例
    integration = StreamlitLLMIntegration()
    
    # 模拟自动初始化
    success = integration.auto_initialize_llm()
    if not success:
        print("❌ LLM初始化失败")
        return False
    
    print("✅ LLM初始化成功")
    
    # 加载测试数据
    test_data = pd.DataFrame({
        'region': ['北京', '上海', '广州', '深圳', '杭州'],
        'product': ['产品A', '产品B', '产品A', '产品C', '产品B'],
        'sales': [100, 200, 150, 300, 250],
        'quantity': [10, 20, 15, 30, 25]
    })
    
    success, error_msg = integration.load_data(test_data, "test_data")
    if not success:
        print(f"❌ 数据加载失败: {error_msg}")
        return False
    
    print("✅ 数据加载成功")
    
    # 模拟前两轮对话
    print("📝 添加前两轮对话到上下文...")
    
    # 第一轮
    integration.add_conversation_round(
        user_message="分析各地区销售情况",
        assistant_message="我已经分析了各地区的销售情况，生成了相应的图表。",
        code="import pandas as pd\nimport matplotlib.pyplot as plt\nresult = df.groupby('region')['sales'].sum()\nplt.bar(result.index, result.values)\nplt.title('各地区销售情况')\nst.pyplot(plt)",
        execution_result={"success": True}
    )
    
    # 第二轮
    integration.add_conversation_round(
        user_message="显示产品销售排名",
        assistant_message="我已经生成了产品销售排名的分析结果。",
        code="import pandas as pd\nresult = df.groupby('product')['sales'].sum().sort_values(ascending=False)\nst.bar_chart(result)",
        execution_result={"success": True}
    )
    
    print("✅ 前两轮对话已添加")
    
    # 第三轮对话 - 这是之前失败的地方
    print("🎯 开始第三轮对话测试...")
    
    instruction = "请将图形换成饼图"
    
    # 使用上下文感知的分析方法
    success, code, error_msg = integration.analyze_data_with_context(
        instruction=instruction,
        use_metadata=False
    )
    
    if not success:
        print(f"❌ 第三轮对话失败: {error_msg}")
        return False
    
    print("✅ 第三轮对话成功生成代码")
    print("生成的代码:")
    print("-" * 40)
    print(code)
    print("-" * 40)
    
    # 检查代码质量
    if '代码生成失败' in code:
        print("❌ 代码包含失败标记")
        return False
    
    if 'import os' in code:
        print("❌ 代码仍包含危险操作")
        return False
    
    if 'pie' in code.lower() or '饼图' in code:
        print("✅ 代码正确响应了饼图请求")
    else:
        print("⚠️ 代码可能没有正确响应饼图请求")
    
    # 测试代码执行
    print("🔧 测试代码执行...")
    exec_success, exec_error = integration.execute_code(code)
    
    if exec_success:
        print("✅ 代码执行成功")
    else:
        print(f"❌ 代码执行失败: {exec_error}")
        return False
    
    return True

if __name__ == "__main__":
    print("🔧 开始测试第三轮对话修复...")
    
    success = test_third_round_conversation()
    
    if success:
        print("\n✅ 第三轮对话修复测试通过！")
        print("🎉 问题已解决，代码生成应该能正常工作了。")
    else:
        print("\n❌ 第三轮对话修复测试失败，需要进一步调试。")
