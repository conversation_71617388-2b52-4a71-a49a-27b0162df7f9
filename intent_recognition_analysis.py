#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
意图识别机制深度分析脚本
详细分析第二轮及后续对话中的意图识别和上下文传递机制
"""

import sys
from pathlib import Path
import re
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def analyze_reference_detection_algorithm():
    """分析引用检测算法的核心机制"""
    print("🔍 引用检测算法深度分析")
    print("=" * 60)
    
    # 模拟引用检测算法
    def _detect_references(instruction: str, reference_tracker: Dict) -> Dict[str, Any]:
        """
        核心引用检测算法（基于实际代码）
        """
        instruction_lower = instruction.lower()
        references = {}
        
        # 第一步：检测时间引用关键词
        time_references = ['之前', '刚才', '上面', '前面', '刚刚', '刚才的', '之前的', '上面的', 
                          '基于', '根据', '参考', '延续', '在这基础上', '接着']
        has_time_reference = any(ref in instruction_lower for ref in time_references)
        
        print(f"📝 用户指令: {instruction}")
        print(f"🔍 检测到时间引用: {has_time_reference}")
        
        if has_time_reference:
            # 第二步：检测具体引用对象
            reference_patterns = {
                'chart': {
                    'keywords': ['图', '图表', 'chart', 'plot', '条形图', '柱状图', '折线图'],
                    'tracker_key': 'last_chart'
                },
                'table': {
                    'keywords': ['表', '表格', 'table', 'dataframe', '数据表'],
                    'tracker_key': 'last_table'
                },
                'analysis': {
                    'keywords': ['分析', '结果', 'analysis', '计算', '统计'],
                    'tracker_key': 'last_analysis'
                }
            }
            
            for ref_type, config in reference_patterns.items():
                if any(word in instruction_lower for word in config['keywords']):
                    tracker_data = reference_tracker.get(config['tracker_key'])
                    if tracker_data:
                        references[ref_type] = tracker_data
                        print(f"  ✅ 检测到{ref_type}引用: {config['keywords'][0]}")
        
        return references
    
    # 测试不同的用户指令
    test_cases = [
        {
            'instruction': '基于刚才的分析，进一步分析地区分布',
            'tracker': {
                'last_analysis': {
                    'code': 'product_sales = df.groupby("产品名称")["销售额"].sum()',
                    'success': True,
                    'timestamp': '2025-08-05T16:00:00'
                }
            }
        },
        {
            'instruction': '修改上面的图表，改成饼图',
            'tracker': {
                'last_chart': {
                    'code': 'st.bar_chart(product_sales)',
                    'success': True,
                    'timestamp': '2025-08-05T16:00:00'
                }
            }
        },
        {
            'instruction': '显示前面表格的详细信息',
            'tracker': {
                'last_table': {
                    'code': 'st.dataframe(df.head())',
                    'success': True,
                    'timestamp': '2025-08-05T16:00:00'
                }
            }
        },
        {
            'instruction': '分析新的数据集',  # 无引用
            'tracker': {}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试案例 {i}:")
        print("-" * 40)
        references = _detect_references(test_case['instruction'], test_case['tracker'])
        print(f"🎯 检测结果: {list(references.keys()) if references else '无引用'}")
        
        if references:
            for ref_type, ref_data in references.items():
                print(f"  📊 {ref_type}: {ref_data['code'][:50]}...")

def analyze_context_building_strategy():
    """分析上下文构建策略"""
    print(f"\n🏗️ 上下文构建策略分析")
    print("=" * 60)
    
    def build_context_for_llm(current_instruction: str, conversation_rounds: List[Dict], 
                             current_summary: Dict = None, max_recent_rounds: int = 3) -> Dict[str, Any]:
        """
        核心上下文构建算法（基于实际代码）
        """
        # 1. 获取最近的对话轮次
        recent_rounds = conversation_rounds[-max_recent_rounds:] if conversation_rounds else []
        
        # 2. 检测引用（简化版）
        references = {}
        instruction_lower = current_instruction.lower()
        time_refs = ['之前', '刚才', '上面', '前面', '基于']
        
        if any(ref in instruction_lower for ref in time_refs):
            # 从最近的轮次中提取可引用的内容
            for i, round_data in enumerate(reversed(recent_rounds)):
                if round_data.get('code') and round_data.get('execution_result', {}).get('success'):
                    if any(word in instruction_lower for word in ['图', 'chart']):
                        if any(chart_word in round_data['code'].lower() for chart_word in ['chart', 'plot']):
                            references['chart'] = {
                                'code': round_data['code'],
                                'success': True,
                                'round': len(recent_rounds) - i
                            }
                    if any(word in instruction_lower for word in ['分析', 'analysis']):
                        references['analysis'] = {
                            'code': round_data['code'],
                            'success': True,
                            'round': len(recent_rounds) - i
                        }
        
        # 3. 构建完整上下文
        context = {
            'has_summary': current_summary is not None,
            'summary': current_summary,
            'recent_rounds': recent_rounds,
            'references': references,
            'current_instruction': current_instruction,
            'total_rounds': len(conversation_rounds)
        }
        
        return context
    
    # 模拟对话历史
    mock_conversation = [
        {
            'user_message': '分析各产品的销售额',
            'assistant_message': '已完成产品销售额分析',
            'code': '''
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
st.bar_chart(product_sales)
best_product = product_sales.index[0]
            ''',
            'execution_result': {'success': True},
            'timestamp': '2025-08-05T16:00:00'
        },
        {
            'user_message': '显示数据的基本信息',
            'assistant_message': '已显示数据基本信息',
            'code': '''
st.write(f"数据形状: {df.shape}")
st.write(f"列名: {list(df.columns)}")
st.dataframe(df.describe())
            ''',
            'execution_result': {'success': True},
            'timestamp': '2025-08-05T16:01:00'
        }
    ]
    
    # 测试不同的第三轮指令
    test_instructions = [
        "基于之前的分析，看看最佳产品的地区分布",
        "修改上面的图表样式",
        "分析一个全新的维度"
    ]
    
    for instruction in test_instructions:
        print(f"\n📝 测试指令: {instruction}")
        print("-" * 40)
        
        context = build_context_for_llm(instruction, mock_conversation)
        
        print(f"📚 历史轮次数: {len(context['recent_rounds'])}")
        print(f"🔗 检测到引用: {list(context['references'].keys()) if context['references'] else '无'}")
        print(f"📊 总轮次: {context['total_rounds']}")
        
        if context['references']:
            for ref_type, ref_data in context['references'].items():
                print(f"  • {ref_type}: 来自第{ref_data['round']}轮对话")

def analyze_prompt_enhancement_mechanism():
    """分析提示词增强机制"""
    print(f"\n⚡ 提示词增强机制分析")
    print("=" * 60)
    
    def build_contextual_prompt(instruction: str, context: str, conversation_context: Dict) -> str:
        """
        构建包含上下文的提示词（基于实际代码）
        """
        prompt_parts = [
            "你是一个专业的数据分析助手，能够理解对话历史并提供连贯的分析。",
            f"当前数据信息：\n{context}",
        ]
        
        # 添加对话摘要
        if conversation_context.get('has_summary') and conversation_context['summary']:
            summary = conversation_context['summary']
            prompt_parts.append(f"对话背景：{summary['summary_text']}")
            
            if summary.get('key_concerns'):
                prompt_parts.append(f"用户关注点：{', '.join(summary['key_concerns'])}")
        
        # 添加最近对话历史
        recent_rounds = conversation_context.get('recent_rounds', [])
        if recent_rounds:
            prompt_parts.append("最近的对话历史：")
            for i, round_data in enumerate(recent_rounds[-3:], 1):  # 最近3轮
                prompt_parts.append(f"第{i}轮 - 用户：{round_data['user_message']}")
                if round_data.get('code'):
                    prompt_parts.append(f"第{i}轮 - 生成代码：\n```python\n{round_data['code']}\n```")
                    
                    # 添加执行结果状态
                    exec_result = round_data.get('execution_result', {})
                    if exec_result.get('success'):
                        prompt_parts.append(f"第{i}轮 - 代码执行：成功")
                    elif exec_result:
                        prompt_parts.append(f"第{i}轮 - 代码执行：失败 - {exec_result.get('error', '未知错误')}")
        
        # 添加引用处理
        references = conversation_context.get('references', {})
        if references:
            prompt_parts.append("引用信息：")
            for ref_type, ref_data in references.items():
                if ref_data and ref_data.get('success'):
                    prompt_parts.append(f"之前的{ref_type}代码：\n```python\n{ref_data['code']}\n```")
        
        # 添加当前指令和指导原则
        prompt_parts.extend([
            f"当前用户问题：{instruction}",
            "",
            "请基于以上对话历史和数据信息，生成相应的Python代码。",
            "",
            "重要指导原则：",
            "1. 如果用户提到'之前的'、'刚才的'、'上面的'等词汇，请参考对话历史和引用信息",
            "2. 可以基于之前的分析结果进行进一步分析或修改",
            "3. 如果需要修改之前的代码，请生成完整的新代码",
            "4. 确保代码能够独立运行，包含必要的导入语句",
            "5. 保持分析的连贯性和逻辑性",
            "6. 如果发现之前的分析有问题，可以提出改进建议"
        ])
        
        return "\n\n".join(prompt_parts)
    
    # 模拟上下文数据
    mock_context = "数据形状: (100, 5)\n列名: ['产品名称', '销售额', '地区', '日期', '销量']"
    
    mock_conversation_context = {
        'has_summary': False,
        'summary': None,
        'recent_rounds': [
            {
                'user_message': '分析各产品的销售额',
                'code': 'product_sales = df.groupby("产品名称")["销售额"].sum()\nst.bar_chart(product_sales)',
                'execution_result': {'success': True}
            }
        ],
        'references': {
            'analysis': {
                'code': 'product_sales = df.groupby("产品名称")["销售额"].sum()',
                'success': True
            }
        },
        'total_rounds': 1
    }
    
    instruction = "基于刚才的分析，进一步分析最佳产品的地区分布"
    
    enhanced_prompt = build_contextual_prompt(instruction, mock_context, mock_conversation_context)
    
    print("🎯 增强后的提示词结构:")
    print("-" * 40)
    
    sections = enhanced_prompt.split('\n\n')
    for i, section in enumerate(sections, 1):
        if section.strip():
            first_line = section.split('\n')[0]
            print(f"{i}. {first_line[:50]}...")
    
    print(f"\n📊 提示词统计:")
    print(f"  总长度: {len(enhanced_prompt)} 字符")
    print(f"  预估Token: {len(enhanced_prompt) // 4}")
    print(f"  包含历史轮次: {len(mock_conversation_context['recent_rounds'])}")
    print(f"  包含引用: {len(mock_conversation_context['references'])}")

if __name__ == "__main__":
    # 执行完整的意图识别分析
    analyze_reference_detection_algorithm()
    analyze_context_building_strategy()
    analyze_prompt_enhancement_mechanism()
    
    print(f"\n🎉 意图识别机制分析完成!")
    print("=" * 60)
