#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试立即改进方案的效果
验证代码优化功能是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_code_optimization():
    """测试代码优化功能"""
    print("🧪 测试立即改进方案效果")
    print("=" * 60)
    
    try:
        # 导入修改后的类
        from core.llm.llm_factory import EnhancedTongyiLLM
        from core.utils.config import TongyiConfig
        
        # 创建一个测试实例
        config = TongyiConfig(
            api_key="test_key",
            model="qwen-plus"
        )
        
        # 创建LLM实例（不需要真实的API调用）
        from core.clients.tongyi_client import TongyiQianwenClient
        from core.processors.code_cleaner import CodeCleaner
        from core.processors.chart_fixer import ChartFixer
        from core.processors.metadata_processor import MetadataProcessor
        
        client = TongyiQianwenClient(config)
        code_cleaner = CodeCleaner(enable_logging=False)
        chart_fixer = ChartFixer(enable_logging=False)
        metadata_processor = MetadataProcessor(enable_logging=False)
        
        llm = EnhancedTongyiLLM(
            client=client,
            code_cleaner=code_cleaner,
            chart_fixer=chart_fixer,
            metadata_processor=metadata_processor,
            enable_logging=False
        )
        
        print("✅ LLM实例创建成功")
        
        # 测试代码优化功能
        test_long_code = '''
import pandas as pd
import streamlit as st

# 计算各产品销售额
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)

# 显示标题
st.subheader("📊 各产品销售额分析")

# 显示条形图
st.bar_chart(product_sales)

# 显示数据表
st.dataframe(product_sales.reset_index())

# 找出最佳产品
best_product = product_sales.index[0]
best_amount = product_sales.iloc[0]

# 显示结果
st.success(f"销售额最高的产品: {best_product} (¥{best_amount:,})")

# 计算总销售额
total_sales = product_sales.sum()

# 显示总销售额
st.write(f"总销售额: ¥{total_sales:,}")

# 计算占比
best_ratio = (best_amount / total_sales * 100).round(2)

# 显示占比
st.info(f"{best_product} 占总销售额的 {best_ratio}%")
        '''
        
        print(f"\n📊 原始代码统计:")
        print(f"  字符数: {len(test_long_code)}")
        print(f"  行数: {len(test_long_code.split(chr(10)))}")
        print(f"  预估Token: {len(test_long_code) // 4}")
        
        # 测试优化功能
        optimized_code = llm._optimize_code_for_context(test_long_code)
        
        print(f"\n📈 优化后代码统计:")
        print(f"  字符数: {len(optimized_code)}")
        print(f"  行数: {len(optimized_code.split(chr(10)))}")
        print(f"  预估Token: {len(optimized_code) // 4}")
        
        # 计算优化效果
        char_reduction = (len(test_long_code) - len(optimized_code)) / len(test_long_code) * 100
        token_reduction = ((len(test_long_code) // 4) - (len(optimized_code) // 4)) / (len(test_long_code) // 4) * 100
        
        print(f"\n🎯 优化效果:")
        print(f"  字符减少: {char_reduction:.1f}%")
        print(f"  Token减少: {token_reduction:.1f}%")
        
        print(f"\n📝 优化后的代码:")
        print("-" * 40)
        print(optimized_code)
        print("-" * 40)
        
        # 测试短代码（应该保持不变）
        test_short_code = '''
import pandas as pd
result = df.groupby('产品')['销售额'].sum()
print(result)
        '''
        
        print(f"\n🧪 测试短代码处理:")
        print(f"原始代码长度: {len(test_short_code)} 字符")
        
        optimized_short = llm._optimize_code_for_context(test_short_code)
        print(f"优化后长度: {len(optimized_short)} 字符")
        
        if len(optimized_short) == len(test_short_code.strip()):
            print("✅ 短代码保持不变（符合预期）")
        else:
            print("⚠️ 短代码发生了变化")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试集成效果"""
    print(f"\n🔗 测试集成效果")
    print("=" * 60)
    
    try:
        # 模拟对话上下文
        mock_conversation_context = {
            'recent_rounds': [
                {
                    'user_message': '分析各产品的销售额',
                    'code': '''
import pandas as pd
import streamlit as st

product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
st.subheader("📊 各产品销售额分析")
st.bar_chart(product_sales)
st.dataframe(product_sales.reset_index())
best_product = product_sales.index[0]
best_amount = product_sales.iloc[0]
st.success(f"销售额最高的产品: {best_product} (¥{best_amount:,})")
total_sales = product_sales.sum()
st.write(f"总销售额: ¥{total_sales:,}")
                    ''',
                    'execution_result': {'success': True}
                }
            ],
            'references': {
                'analysis': {
                    'code': '''
df_summary = df.describe()
st.subheader("数据概览")
st.dataframe(df_summary)
st.write("数据分析完成")
for col in df.columns:
    st.write(f"列 {col} 的统计信息:")
    st.write(df[col].describe())
                    ''',
                    'success': True
                }
            }
        }
        
        # 计算原始提示词长度
        original_prompt_parts = []
        
        # 模拟原始方式构建提示词
        for i, round_data in enumerate(mock_conversation_context['recent_rounds'], 1):
            original_prompt_parts.append(f"第{i}轮 - 用户：{round_data['user_message']}")
            if round_data.get('code'):
                original_prompt_parts.append(f"第{i}轮 - 生成代码：\n```python\n{round_data['code']}\n```")
        
        for ref_type, ref_data in mock_conversation_context['references'].items():
            if ref_data and ref_data.get('success'):
                original_prompt_parts.append(f"之前的{ref_type}代码：\n```python\n{ref_data['code']}\n```")
        
        original_prompt = '\n\n'.join(original_prompt_parts)
        
        print(f"📊 原始提示词统计:")
        print(f"  字符数: {len(original_prompt)}")
        print(f"  预估Token: {len(original_prompt) // 4}")
        
        # 模拟优化后的方式
        from core.llm.llm_factory import EnhancedTongyiLLM
        from core.utils.config import TongyiConfig
        from core.clients.tongyi_client import TongyiQianwenClient
        from core.processors.code_cleaner import CodeCleaner
        from core.processors.chart_fixer import ChartFixer
        from core.processors.metadata_processor import MetadataProcessor
        
        config = TongyiConfig(api_key="test", model="qwen-plus")
        client = TongyiQianwenClient(config)
        llm = EnhancedTongyiLLM(
            client=client,
            code_cleaner=CodeCleaner(False),
            chart_fixer=ChartFixer(False),
            metadata_processor=MetadataProcessor(False),
            enable_logging=False
        )
        
        optimized_prompt_parts = []
        
        # 模拟优化后的方式
        for i, round_data in enumerate(mock_conversation_context['recent_rounds'], 1):
            optimized_prompt_parts.append(f"第{i}轮 - 用户：{round_data['user_message']}")
            if round_data.get('code'):
                code = round_data['code']
                if len(code) > 400:
                    optimized_code = llm._optimize_code_for_context(code)
                    optimized_prompt_parts.append(f"第{i}轮 - 核心逻辑：\n```python\n{optimized_code}\n```")
                else:
                    optimized_prompt_parts.append(f"第{i}轮 - 生成代码：\n```python\n{code}\n```")
        
        for ref_type, ref_data in mock_conversation_context['references'].items():
            if ref_data and ref_data.get('success'):
                ref_code = ref_data['code']
                if len(ref_code) > 300:
                    optimized_ref_code = llm._optimize_code_for_context(ref_code)
                    optimized_prompt_parts.append(f"之前的{ref_type}核心逻辑：\n```python\n{optimized_ref_code}\n```")
                else:
                    optimized_prompt_parts.append(f"之前的{ref_type}代码：\n```python\n{ref_code}\n```")
        
        optimized_prompt = '\n\n'.join(optimized_prompt_parts)
        
        print(f"\n📈 优化后提示词统计:")
        print(f"  字符数: {len(optimized_prompt)}")
        print(f"  预估Token: {len(optimized_prompt) // 4}")
        
        # 计算整体优化效果
        total_reduction = (len(original_prompt) - len(optimized_prompt)) / len(original_prompt) * 100
        token_reduction = ((len(original_prompt) // 4) - (len(optimized_prompt) // 4)) / (len(original_prompt) // 4) * 100
        
        print(f"\n🎯 整体优化效果:")
        print(f"  字符减少: {total_reduction:.1f}%")
        print(f"  Token减少: {token_reduction:.1f}%")
        
        if total_reduction >= 20:
            print("✅ 达到预期效果（Token减少20-40%）")
        else:
            print("⚠️ 优化效果低于预期")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 立即改进方案效果测试")
    print("=" * 80)
    
    # 运行测试
    test1_success = test_code_optimization()
    test2_success = test_integration()
    
    print(f"\n📋 测试结果总结:")
    print("=" * 60)
    print(f"  代码优化功能: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"  集成效果测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 立即改进方案部署成功！")
        print("✅ 零风险修改完成")
        print("✅ 预期Token减少20-40%")
        print("✅ 所有功能正常工作")
        print("\n💡 建议：现在可以在实际应用中观察效果")
    else:
        print(f"\n⚠️ 部分测试失败，请检查修改")
        print("🔄 如需回退，执行：cp core/llm/llm_factory.py.backup core/llm/llm_factory.py")
