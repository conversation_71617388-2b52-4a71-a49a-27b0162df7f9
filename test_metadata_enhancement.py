#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试元数据增强功能
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.integrations.streamlit_integration import StreamlitLLMIntegration
from core.processors.metadata_processor import MetadataProcessor

def test_metadata_extraction():
    """测试元数据提取功能"""
    print("🧪 测试元数据提取功能...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑', '手机', '笔记本电脑'],
        '销售额': [8500, 6200, 3200, 4500, 9200],
        '销量': [5, 3, 8, 15, 6],
        '地区': ['北京', '上海', '广州', '深圳', '北京'],
        '销售员': ['张三', '李四', '王五', '赵六', '张三']
    })
    
    # 创建元数据处理器
    processor = MetadataProcessor()
    
    # 提取元数据
    metadata = processor.extract_dataframe_metadata(test_data, "销售数据")
    
    print("提取的元数据:")
    print("-" * 60)
    
    # 显示列信息
    print("📊 列信息:")
    for col_name, col_info in metadata['columns'].items():
        print(f"  • {col_name}:")
        print(f"    - 数据类型: {col_info.get('data_type', 'unknown')}")
        print(f"    - 语义类型: {col_info.get('semantic_type', 'unknown')}")
        print(f"    - 业务含义: {col_info.get('business_meaning', '未知')}")
        print(f"    - 推荐分析: {col_info.get('recommended_analysis', '未知')}")
        if 'unique_values' in col_info:
            print(f"    - 取值: {col_info['unique_values']}")
        elif 'value_range' in col_info:
            print(f"    - 范围: {col_info['value_range']}")
    
    # 显示表信息
    print("\n📋 表信息:")
    table_info = metadata.get('table_info', {})
    print(f"  • 业务描述: {table_info.get('business_description', '未知')}")
    print(f"  • 推荐图表: {', '.join(table_info.get('recommended_charts', []))}")
    print(f"  • 分析建议: {'; '.join(table_info.get('analysis_suggestions', []))}")
    
    return metadata

def test_metadata_formatting():
    """测试元数据格式化功能"""
    print("\n🧪 测试元数据格式化功能...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '销售额': [8500, 6200, 3200, 4500, 9200],
        '地区': ['北京', '上海', '广州', '深圳', '北京'],
    })
    
    processor = MetadataProcessor()
    metadata = processor.extract_dataframe_metadata(test_data, "测试数据")
    
    # 格式化元数据
    formatted_metadata = processor.format_metadata_for_prompt(metadata)
    
    print("格式化后的元数据:")
    print("-" * 60)
    print(formatted_metadata)
    print("-" * 60)
    
    return formatted_metadata

def test_enhanced_prompt():
    """测试增强后的提示词"""
    print("\n🧪 测试增强后的提示词...")
    
    # 创建集成实例
    integration = StreamlitLLMIntegration()
    
    # 自动初始化LLM
    success = integration.auto_initialize_llm()
    if not success:
        print("❌ LLM初始化失败")
        return False
    
    print("✅ LLM初始化成功")
    
    # 加载测试数据
    test_data = pd.DataFrame({
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑'],
        '销售额': [8500, 6200, 3200],
        '销量': [5, 3, 8],
        '地区': ['北京', '上海', '广州'],
        '销售员': ['张三', '李四', '王五']
    })
    
    success, error_msg = integration.load_data(test_data, "sales_data")
    if not success:
        print(f"❌ 数据加载失败: {error_msg}")
        return False
    
    print("✅ 数据加载成功")
    
    # 测试带元数据的分析
    print("🎯 测试带元数据的分析...")
    
    instruction = "分析各地区的销售情况"
    
    # 使用元数据增强
    success, code, error_msg = integration.analyze_data_with_context(
        instruction=instruction,
        use_metadata=True  # 启用元数据
    )
    
    if not success:
        print(f"❌ 带元数据的分析失败: {error_msg}")
        return False
    
    print("✅ 带元数据的分析成功")
    print("生成的代码:")
    print("-" * 60)
    print(code)
    print("-" * 60)
    
    # 测试代码执行
    exec_success, exec_error = integration.execute_code(code)
    if exec_success:
        print("✅ 代码执行成功")
    else:
        print(f"❌ 代码执行失败: {exec_error}")
        return False
    
    return True

def compare_with_without_metadata():
    """对比有无元数据的提示词差异"""
    print("\n🧪 对比有无元数据的提示词差异...")
    
    integration = StreamlitLLMIntegration()
    success = integration.auto_initialize_llm()
    if not success:
        return False
    
    test_data = pd.DataFrame({
        '销售额': [8500, 6200, 3200],
        '地区': ['北京', '上海', '广州'],
    })
    
    integration.load_data(test_data, "test_data")
    
    instruction = "分析各地区销售额"
    
    print("📝 不使用元数据的提示词长度:")
    success1, code1, _ = integration.analyze_data_with_context(instruction, use_metadata=False)
    if success1:
        print(f"  代码长度: {len(code1)} 字符")
    
    print("📝 使用元数据的提示词长度:")
    success2, code2, _ = integration.analyze_data_with_context(instruction, use_metadata=True)
    if success2:
        print(f"  代码长度: {len(code2)} 字符")
        print(f"  增强效果: {'✅ 有改善' if len(code2) > len(code1) else '⚠️ 无明显差异'}")
    
    return success1 and success2

if __name__ == "__main__":
    print("🔧 开始测试元数据增强功能...")
    print("=" * 60)
    
    # 测试1: 元数据提取
    metadata = test_metadata_extraction()
    
    # 测试2: 元数据格式化
    formatted = test_metadata_formatting()
    
    # 测试3: 增强后的提示词
    enhanced_success = test_enhanced_prompt()
    
    # 测试4: 对比效果
    comparison_success = compare_with_without_metadata()
    
    print("=" * 60)
    if enhanced_success and comparison_success:
        print("🎉 元数据增强功能测试通过！")
        print("✨ 系统现在能够提供更智能的数据分析建议。")
    else:
        print("❌ 元数据增强功能测试失败，需要进一步调试。")
