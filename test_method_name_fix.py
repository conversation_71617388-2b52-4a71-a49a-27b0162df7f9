#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM方法名修复
验证是否能正确调用LLM的call方法
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_llm_method_fix():
    """测试LLM方法名修复"""
    print("🔧 测试LLM方法名修复")
    print("=" * 60)
    
    try:
        from core.utils.context_manager import ContextManager
        import streamlit as st
        
        # 模拟streamlit session state
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self._state = {}
                
                def __getattr__(self, key):
                    return self._state.get(key)
                
                def __setattr__(self, key, value):
                    if key.startswith('_'):
                        super().__setattr__(key, value)
                    else:
                        self._state[key] = value
                
                def __contains__(self, key):
                    return key in self._state
            
            st.session_state = MockSessionState()
        
        # 模拟LLM响应对象
        class MockLLMResponse:
            def __init__(self, content):
                self.content = content
        
        # 模拟LLM客户端 - 使用正确的call方法
        class MockLLMClient:
            def call(self, prompt, **kwargs):
                print(f"✅ LLM.call()方法成功调用！")
                print(f"   提示词长度: {len(prompt)} 字符")
                
                # 智能响应
                if "进一步" in prompt:
                    content = '''
{
    "has_reference": true,
    "confidence": 0.95,
    "reasoning": "用户使用'进一步'明确表示要基于之前的地区销售额分析结果进行扩展分析",
    "reference_type": "continuation"
}
                    '''
                else:
                    content = '''
{
    "has_reference": false,
    "confidence": 0.1,
    "reasoning": "没有发现明确的引用意图",
    "reference_type": "independent"
}
                    '''
                
                return MockLLMResponse(content)
        
        # 模拟integration
        class MockIntegration:
            def get_llm_instance(self):
                return MockLLMClient()
        
        # 设置正确的session state
        st.session_state.integration = MockIntegration()
        st.session_state.context_manager = {
            'conversation_rounds': [
                {
                    'user_message': '分析2024年各地区销售额',
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'execution_result': {'success': True}
                }
            ],
            'reference_tracker': {
                'last_analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'success': True,
                    'timestamp': '2025-08-05T18:41:28'
                }
            }
        }
        
        # 测试
        context_manager = ContextManager(enable_logging=True)
        
        print("📝 测试指令: 进一步分析销售员的销售情况")
        print("-" * 40)
        
        references = context_manager._detect_references("进一步分析销售员的销售情况")
        
        if references:
            print(f"🎉 成功！检测到引用: {list(references.keys())}")
            for ref_type, ref_data in references.items():
                print(f"   • {ref_type}: {ref_data.get('code', '')[:50]}...")
            return True
        else:
            print(f"❌ 失败：未检测到引用")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_method_name_issue():
    """分析方法名问题"""
    print(f"\n🔍 方法名问题分析")
    print("=" * 60)
    
    print("❌ 错误的方法调用:")
    print("-" * 40)
    print("  response = llm_client.generate(prompt)")
    print("  ↑ 错误：EnhancedTongyiLLM没有generate方法")
    
    print(f"\n✅ 正确的方法调用:")
    print("-" * 40)
    print("  llm_response = llm_client.call(prompt)")
    print("  response = llm_response.content")
    print("  ↑ 正确：使用BaseLLM的call方法")
    
    print(f"\n📊 从日志分析:")
    print("-" * 40)
    
    log_analysis = [
        "❌ 'EnhancedTongyiLLM' object has no attribute 'generate'",
        "✅ EnhancedTongyiLLM继承自BaseLLM",
        "✅ BaseLLM有call方法，返回LLMResponse对象",
        "✅ LLMResponse.content包含实际的响应文本",
        "🎯 问题：我调用了不存在的方法名"
    ]
    
    for analysis in log_analysis:
        print(f"  {analysis}")

def show_expected_result():
    """显示预期结果"""
    print(f"\n🚀 修复后的预期效果")
    print("=" * 60)
    
    print("✅ 第二轮对话日志应该显示:")
    print("-" * 40)
    print("🤖 纯LLM意图分析:")
    print("   有引用: True")
    print("   置信度: 0.95")
    print("   LLM推理: 用户使用'进一步'明确表示要基于之前的地区销售额分析结果...")
    print("   最终结果: ['analysis']")
    
    print(f"\n❌ 而不是之前的:")
    print("-" * 40)
    print("ERROR - LLM意图分析失败: 'EnhancedTongyiLLM' object has no attribute 'generate'")
    print("🔗 检测到的引用: 无")
    
    print(f"\n🎯 最终效果:")
    print("-" * 40)
    effects = [
        "✅ LLM意图分析正常工作",
        "✅ 能识别'进一步'的引用意图",
        "✅ 传递正确的引用信息给代码生成",
        "✅ 生成地区+销售员的组合分析",
        "✅ 实现真正的对话连贯性"
    ]
    
    for effect in effects:
        print(f"  {effect}")

if __name__ == "__main__":
    print("🔧 LLM方法名修复测试")
    print("=" * 80)
    
    # 分析问题
    analyze_method_name_issue()
    
    # 测试修复
    success = test_llm_method_fix()
    
    # 显示预期结果
    show_expected_result()
    
    print(f"\n📊 测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("\n🎉 方法名修复成功！")
        print("✅ 现在使用正确的call方法调用LLM")
        print("✅ LLM意图分析应该能正常工作")
        print("💡 请重启应用并重新测试您的两轮对话")
    else:
        print("\n⚠️ 仍需进一步调试")
    
    print(f"\n🎯 关键修复:")
    print("从错误的generate()方法改为正确的call()方法")
    print("这应该能解决'object has no attribute generate'的错误！")
