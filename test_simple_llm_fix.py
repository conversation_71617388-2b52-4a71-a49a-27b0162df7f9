#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试LLM客户端获取修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_simple_llm_access():
    """简单测试LLM访问"""
    print("🔧 简单测试LLM客户端访问")
    print("=" * 60)
    
    try:
        from core.utils.context_manager import ContextManager
        import streamlit as st
        
        # 模拟streamlit session state
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self._state = {}
                
                def __getattr__(self, key):
                    return self._state.get(key)
                
                def __setattr__(self, key, value):
                    if key.startswith('_'):
                        super().__setattr__(key, value)
                    else:
                        self._state[key] = value
                
                def __contains__(self, key):
                    return key in self._state
            
            st.session_state = MockSessionState()
        
        # 模拟LLM客户端
        class MockLLMClient:
            def generate(self, prompt):
                print(f"✅ LLM成功调用！提示词长度: {len(prompt)} 字符")
                
                if "进一步" in prompt:
                    return '''
{
    "has_reference": true,
    "confidence": 0.95,
    "reasoning": "用户使用'进一步'明确表示要基于之前的分析结果进行扩展",
    "reference_type": "continuation"
}
                    '''
                return '''
{
    "has_reference": false,
    "confidence": 0.1,
    "reasoning": "没有发现引用意图",
    "reference_type": "independent"
}
                '''
        
        # 模拟integration
        class MockIntegration:
            def get_llm_instance(self):
                return MockLLMClient()
        
        # 设置正确的session state
        st.session_state.integration = MockIntegration()
        st.session_state.context_manager = {
            'conversation_rounds': [
                {
                    'user_message': '分析2024年各地区销售额',
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'execution_result': {'success': True}
                }
            ],
            'reference_tracker': {
                'last_analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'success': True,
                    'timestamp': '2025-08-05T18:24:12'
                }
            }
        }
        
        # 测试
        context_manager = ContextManager(enable_logging=True)
        
        print("📝 测试指令: 进一步分析销售员的销售情况")
        print("-" * 40)
        
        references = context_manager._detect_references("进一步分析销售员的销售情况")
        
        if references:
            print(f"🎉 成功！检测到引用: {list(references.keys())}")
            return True
        else:
            print(f"❌ 失败：未检测到引用")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_summary():
    """显示修复总结"""
    print(f"\n📋 修复总结")
    print("=" * 60)
    
    print("🎯 您的观点完全正确：")
    print("-" * 40)
    print("✅ 智能问答功能正常 → LLM本身没问题")
    print("✅ 应该直接引用大模型变量 → 不需要备用方案")
    print("❌ 我的代码获取方式错误 → 真正的问题所在")
    
    print(f"\n🔧 关键修复：")
    print("-" * 40)
    print("❌ 错误方式: getattr(st.session_state, 'llm', None)")
    print("✅ 正确方式: st.session_state.integration.get_llm_instance()")
    
    print(f"\n🚀 预期效果：")
    print("-" * 40)
    print("✅ LLM意图分析正常工作")
    print("✅ 能识别'进一步'等引用意图")
    print("✅ 第二轮对话生成组合分析代码")
    print("✅ 日志显示LLM分析过程而不是'LLM不可用'")

if __name__ == "__main__":
    print("🔧 LLM客户端获取修复验证")
    print("=" * 80)
    
    success = test_simple_llm_access()
    
    show_fix_summary()
    
    print(f"\n📊 测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("\n🎉 修复成功！现在LLM客户端能正确获取并调用。")
        print("💡 请重启应用并重新测试您的两轮对话。")
    else:
        print("\n⚠️ 仍需进一步调试。")
    
    print(f"\n🎯 核心要点：")
    print("您说得对，问题不是LLM不可用，而是我的获取方式错误！")
    print("现在通过正确的integration.get_llm_instance()获取，应该能正常工作。")
