"""
LLM工厂类 - 负责创建和配置完整的LLM实例
"""

from typing import Optional, Dict, Any
from .tongyi_client import TongyiQianwenClient
from ..processors.code_cleaner import CodeCleaner
from ..processors.chart_fixer import ChartFixer
from ..processors.metadata_processor import MetadataProcessor
from ..utils.config import TongyiConfig
from ..utils.logger import get_app_logger


class EnhancedTongyiLLM:
    """
    增强的通义千问LLM
    
    组合了API客户端和各种处理器，提供完整的LLM功能。
    """
    
    def __init__(
        self,
        client: TongyiQianwenClient,
        code_cleaner: Optional[CodeCleaner] = None,
        chart_fixer: Optional[ChartFixer] = None,
        metadata_processor: Optional[MetadataProcessor] = None,
        enable_logging: bool = True
    ):
        """
        初始化增强LLM
        
        Args:
            client: 通义千问API客户端
            code_cleaner: 代码清理器
            chart_fixer: 图表修复器
            metadata_processor: 元数据处理器
            enable_logging: 是否启用日志记录
        """
        self.client = client
        self.code_cleaner = code_cleaner or CodeCleaner(enable_logging)
        self.chart_fixer = chart_fixer or ChartFixer(enable_logging)
        self.metadata_processor = metadata_processor or MetadataProcessor(enable_logging)
        
        self.logger = get_app_logger() if enable_logging else None
        
        # 功能开关
        self.enable_chart_fix = True
        self.enable_metadata = True  # 启用元数据功能
    
    def analyze_data_with_context(self, instruction: str, context: str,
                                 conversation_context: Optional[Dict] = None,
                                 metadata: Optional[Dict] = None,
                                 table_name: str = "data") -> str:
        """
        基于对话上下文的数据分析

        Args:
            instruction: 用户指令
            context: 数据上下文
            conversation_context: 对话上下文信息
            metadata: 元数据
            table_name: 表名

        Returns:
            生成的代码
        """
        if self.logger:
            self.logger.info(f"开始上下文感知的数据分析 - 指令: {instruction[:50]}...")

        try:
            # 1. 构建增强的提示词
            if conversation_context:
                enhanced_prompt = self._build_contextual_prompt(
                    instruction, context, conversation_context, metadata, table_name
                )
            else:
                # 降级到原有方法
                return self.analyze_data(instruction, context, metadata, table_name)

            # 2. 调用LLM API
            response = self.client.call(instruction=enhanced_prompt, context="")
            raw_code = response.content

            if self.logger:
                self.logger.info(f"LLM响应获取成功 - tokens: {response.tokens_used}")

            # 3. 清理代码
            cleaned_code = self.code_cleaner.clean(raw_code)

            # 4. 修复图表（如果启用）
            if self.enable_chart_fix:
                final_code = self.chart_fixer.fix_charts(cleaned_code, instruction)
            else:
                final_code = cleaned_code

            if self.logger:
                self.logger.info("上下文感知数据分析完成")

            return final_code

        except Exception as e:
            error_msg = f"上下文感知数据分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)

            # 降级到基础分析
            if self.logger:
                self.logger.info("降级到基础数据分析模式")
            return self.analyze_data(instruction, context, metadata, table_name)

    def _build_contextual_prompt(self, instruction: str, context: str,
                               conversation_context: Dict, metadata: Optional[Dict],
                               table_name: str) -> str:
        """
        构建包含对话上下文的提示词

        Args:
            instruction: 当前指令
            context: 数据上下文
            conversation_context: 对话上下文
            metadata: 元数据
            table_name: 表名

        Returns:
            增强的提示词
        """
        prompt_parts = [
            "你是一个专业的数据分析助手，能够理解对话历史并提供连贯的分析。",
            f"当前数据信息：\n{context}",
        ]

        # 添加元数据信息
        if metadata and self.enable_metadata:
            metadata_text = self.metadata_processor.format_metadata_for_prompt(metadata)
            prompt_parts.append(f"数据元数据：\n{metadata_text}")

        # 添加对话摘要
        if conversation_context.get('has_summary') and conversation_context['summary']:
            summary = conversation_context['summary']
            prompt_parts.append(f"对话背景：{summary['summary_text']}")

            if summary['key_concerns']:
                prompt_parts.append(f"用户关注点：{', '.join(summary['key_concerns'])}")

        # 添加最近对话历史
        recent_rounds = conversation_context.get('recent_rounds', [])
        if recent_rounds:
            prompt_parts.append("最近的对话历史：")
            for i, round_data in enumerate(recent_rounds[-3:], 1):  # 最近3轮
                prompt_parts.append(f"第{i}轮 - 用户：{round_data['user_message']}")
                if round_data.get('code'):
                    code = round_data['code']

                    # 智能代码摘要：长代码只保留核心逻辑
                    if len(code) > 400:  # 超过400字符的代码进行优化
                        optimized_code = self._optimize_code_for_context(code)
                        prompt_parts.append(f"第{i}轮 - 核心逻辑：\n```python\n{optimized_code}\n```")
                    else:
                        prompt_parts.append(f"第{i}轮 - 生成代码：\n```python\n{code}\n```")

                    # 添加执行结果状态
                    exec_result = round_data.get('execution_result', {})
                    if exec_result.get('success'):
                        prompt_parts.append(f"第{i}轮 - 代码执行：成功")
                    elif exec_result:
                        prompt_parts.append(f"第{i}轮 - 代码执行：失败 - {exec_result.get('error', '未知错误')}")

        # 添加引用处理（优化版）
        references = conversation_context.get('references', {})
        if references:
            prompt_parts.append("引用信息：")
            for ref_type, ref_data in references.items():
                if ref_data and ref_data.get('success'):
                    ref_code = ref_data['code']
                    if len(ref_code) > 300:  # 引用代码也进行优化
                        optimized_ref_code = self._optimize_code_for_context(ref_code)
                        prompt_parts.append(f"之前的{ref_type}核心逻辑：\n```python\n{optimized_ref_code}\n```")
                    else:
                        prompt_parts.append(f"之前的{ref_type}代码：\n```python\n{ref_code}\n```")

        # 添加当前指令和指导原则
        prompt_parts.extend([
            f"当前用户问题：{instruction}",
            "",
            "请基于以上对话历史和数据信息，生成相应的Python代码。",
            "",
            "重要指导原则：",
            "1. 如果用户提到'之前的'、'刚才的'、'上面的'、'在这基础上'、'基于'等词汇，请参考对话历史和引用信息",
            "2. 当用户说'在这基础上'或'基于'时，必须复用之前分析中的变量和结果，进行扩展分析",
            "3. 可以基于之前的分析结果进行进一步分析或修改",
            "4. 如果需要修改之前的代码，请生成完整的新代码",
            "5. 确保代码能够独立运行，包含必要的导入语句",
            "6. 保持分析的连贯性和逻辑性",
            "7. 如果发现之前的分析有问题，可以提出改进建议",
            "8. 注意：如果历史代码中定义了变量（如region_sales），新代码应该复用这些变量"
        ])

        final_prompt = "\n\n".join(prompt_parts)

        # 详细日志输出 - 发送给LLM的完整提示词
        if self.logger:
            self.logger.info("=" * 100)
            self.logger.info("🤖 发送给大模型的完整提示词:")
            self.logger.info("-" * 100)
            self.logger.info(final_prompt)
            self.logger.info("-" * 100)
            self.logger.info(f"提示词总长度: {len(final_prompt)} 字符")
            self.logger.info(f"预估Token数: {len(final_prompt) // 4} tokens")
            self.logger.info("=" * 100)

        return final_prompt

    def analyze_data(self, instruction: str, context: str, metadata: Optional[Dict] = None, table_name: str = "data") -> str:
        """
        分析数据并生成代码

        Args:
            instruction: 用户指令
            context: 数据上下文
            metadata: 元数据（可选）
            table_name: 表格名称，用于获取业务元数据

        Returns:
            处理后的Python代码
        """
        if self.logger:
            self.logger.info(f"开始分析数据 - 指令: {instruction[:50]}...")
        
        try:
            # 1. 构建提示词（传递表格名称）
            if self.enable_metadata:
                prompt = self.metadata_processor.enhance_prompt(instruction, context, metadata, table_name)
            else:
                prompt = self.metadata_processor.enhance_prompt(instruction, context, None, table_name)
            
            # 2. 调用LLM API
            response = self.client.call(instruction=prompt, context="")
            raw_code = response.content
            
            if self.logger:
                self.logger.info(f"LLM响应获取成功 - tokens: {response.tokens_used}")
            
            # 3. 清理代码
            cleaned_code = self.code_cleaner.clean(raw_code)
            
            # 4. 修复图表（如果启用）
            if self.enable_chart_fix:
                final_code = self.chart_fixer.fix_charts(cleaned_code, instruction)
            else:
                final_code = cleaned_code
            
            if self.logger:
                self.logger.info("数据分析完成")
            
            return final_code
            
        except Exception as e:
            error_msg = f"数据分析失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
            return f"# {error_msg}\nst.error('{error_msg}')"
    
    def set_chart_fix_enabled(self, enabled: bool):
        """设置图表修复功能开关"""
        self.enable_chart_fix = enabled
        if self.logger:
            self.logger.info(f"图表修复功能: {'启用' if enabled else '禁用'}")
    
    def set_metadata_enabled(self, enabled: bool):
        """设置元数据功能开关"""
        self.enable_metadata = enabled
        if self.logger:
            self.logger.info(f"元数据功能: {'启用' if enabled else '禁用'}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取LLM使用统计"""
        return self.client.get_stats()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return self.client.get_model_info()

    def _optimize_code_for_context(self, code: str) -> str:
        """
        优化代码用于上下文传递
        保留核心逻辑，移除冗余的展示代码

        Args:
            code: 原始代码

        Returns:
            优化后的代码
        """
        lines = code.split('\n')
        optimized_lines = []

        # 保留的核心逻辑关键词
        core_keywords = [
            'df.groupby', '=', 'sum()', 'mean()', 'count()', 'max()', 'min()',
            'sort_values', 'index[', 'iloc[', 'filter', 'query', 'merge',
            'import ', 'from ', 'def ', 'class ', 'if ', 'for ', 'while '
        ]

        # 需要简化的展示代码关键词
        display_keywords = [
            'st.subheader', 'st.write', 'st.success', 'st.info', 'st.warning',
            'st.dataframe', 'st.table', 'st.bar_chart', 'st.line_chart',
            'plt.', 'fig.', '.show()', 'print('
        ]

        skipped_display_lines = 0

        for line in lines:
            line_stripped = line.strip()

            # 跳过空行和注释
            if not line_stripped or line_stripped.startswith('#'):
                continue

            # 检查是否是核心逻辑
            is_core_logic = any(keyword in line for keyword in core_keywords)
            is_display_code = any(keyword in line for keyword in display_keywords)

            if is_core_logic:
                optimized_lines.append(line)
            elif is_display_code:
                skipped_display_lines += 1
                # 只保留第一个展示语句作为示例
                if skipped_display_lines == 1:
                    optimized_lines.append(f"# ... 展示逻辑 (共{self._count_display_lines(lines)}行)")
            else:
                # 其他逻辑也保留
                optimized_lines.append(line)

        # 如果优化后的代码太短，返回原代码的前10行
        if len(optimized_lines) < 3:
            return '\n'.join(lines[:10]) + ('\n# ... (代码已截断)' if len(lines) > 10 else '')

        return '\n'.join(optimized_lines)

    def _count_display_lines(self, lines: list) -> int:
        """计算展示代码的行数"""
        display_keywords = [
            'st.subheader', 'st.write', 'st.success', 'st.info', 'st.warning',
            'st.dataframe', 'st.table', 'st.bar_chart', 'st.line_chart',
            'plt.', 'fig.', '.show()', 'print('
        ]

        count = 0
        for line in lines:
            if any(keyword in line for keyword in display_keywords):
                count += 1
        return count


class LLMFactory:
    """
    LLM工厂类
    
    负责创建和配置各种LLM实例。
    """
    
    @staticmethod
    def create_tongyi_llm(
        config: Optional[TongyiConfig] = None,
        enable_chart_fix: bool = True,
        enable_metadata: bool = True,  # 默认启用元数据
        enable_logging: bool = True
    ) -> EnhancedTongyiLLM:
        """
        创建增强的通义千问LLM实例
        
        Args:
            config: 通义千问配置（如果为None，从环境变量加载）
            enable_chart_fix: 是否启用图表修复
            enable_metadata: 是否启用元数据支持
            enable_logging: 是否启用日志记录
            
        Returns:
            配置好的增强LLM实例
        """
        # 如果没有提供配置，从环境变量加载
        if config is None:
            config = TongyiConfig.from_env()
        
        # 更新配置中的功能开关
        config.enable_chart_fix = enable_chart_fix
        config.enable_metadata = enable_metadata
        config.enable_logging = enable_logging
        
        # 创建API客户端
        client = TongyiQianwenClient(config)
        
        # 创建处理器
        code_cleaner = CodeCleaner(enable_logging)
        chart_fixer = ChartFixer(enable_logging)
        metadata_processor = MetadataProcessor(enable_logging)
        
        # 创建增强LLM
        enhanced_llm = EnhancedTongyiLLM(
            client=client,
            code_cleaner=code_cleaner,
            chart_fixer=chart_fixer,
            metadata_processor=metadata_processor,
            enable_logging=enable_logging
        )
        
        # 设置功能开关
        enhanced_llm.set_chart_fix_enabled(enable_chart_fix)
        enhanced_llm.set_metadata_enabled(enable_metadata)
        
        return enhanced_llm
    
    @staticmethod
    def create_basic_tongyi_llm(config: Optional[TongyiConfig] = None) -> EnhancedTongyiLLM:
        """
        创建基础的通义千问LLM实例（最小功能）
        
        Args:
            config: 通义千问配置
            
        Returns:
            基础LLM实例
        """
        return LLMFactory.create_tongyi_llm(
            config=config,
            enable_chart_fix=False,
            enable_metadata=False,
            enable_logging=False
        )
    
    @staticmethod
    def create_full_featured_tongyi_llm(config: Optional[TongyiConfig] = None) -> EnhancedTongyiLLM:
        """
        创建全功能的通义千问LLM实例
        
        Args:
            config: 通义千问配置
            
        Returns:
            全功能LLM实例
        """
        return LLMFactory.create_tongyi_llm(
            config=config,
            enable_chart_fix=True,
            enable_metadata=True,
            enable_logging=True
        )
    
    @staticmethod
    def get_available_models() -> Dict[str, Dict[str, Any]]:
        """获取可用的模型列表"""
        return TongyiQianwenClient.RECOMMENDED_MODELS
    
    @staticmethod
    def validate_config(config: TongyiConfig) -> tuple[bool, str]:
        """
        验证配置是否有效
        
        Args:
            config: 要验证的配置
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            client = TongyiQianwenClient(config)
            return client.validate_config(), ""
        except Exception as e:
            return False, str(e)
