#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第二轮对话修复
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.integrations.streamlit_integration import StreamlitLLMIntegration

def test_second_round_with_os_operations():
    """测试包含os操作的第二轮对话"""
    print("🧪 测试包含os操作的第二轮对话...")
    
    # 创建集成实例
    integration = StreamlitLLMIntegration()
    
    # 自动初始化LLM
    success = integration.auto_initialize_llm()
    if not success:
        print("❌ LLM初始化失败")
        return False
    
    print("✅ LLM初始化成功")
    
    # 加载测试数据
    test_data = pd.DataFrame({
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑', '手机'],
        '销售额': [8500, 6200, 3200, 4500],
        '销量': [5, 3, 8, 15],
        '地区': ['北京', '上海', '广州', '深圳'],
        '销售员': ['张三', '李四', '王五', '赵六']
    })
    
    success, error_msg = integration.load_data(test_data, "sales_data")
    if not success:
        print(f"❌ 数据加载失败: {error_msg}")
        return False
    
    print("✅ 数据加载成功")
    
    # 模拟第一轮对话（包含os操作）
    print("📝 添加第一轮对话（包含os操作）...")
    
    first_round_code = """
import pandas as pd
import os
from datetime import datetime

# 分析各地区销售额
df['日期'] = pd.to_datetime(df['日期'])
sales_by_region = df.groupby('地区')['销售额'].sum().reset_index()
st.write("各地区销售额总和：")
st.dataframe(sales_by_region)
st.bar_chart(sales_by_region, x='地区', y='销售额')

# 保存图表数据（用于后续分析）
chart_data_dir = "charts"
os.makedirs(chart_data_dir, exist_ok=True)

# 保存图表数据
if 'df' in locals():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    chart_file = os.path.join(chart_data_dir, f"chart_data_{timestamp}.csv")
    df.to_csv(chart_file, index=False)
    st.info(f"图表数据已保存到: {chart_file}")
"""
    
    integration.add_conversation_round(
        user_message="分析2024年各地区销售额",
        assistant_message="我已经分析了各地区的销售额。",
        code=first_round_code,
        execution_result={"success": True}
    )
    
    print("✅ 第一轮对话已添加")
    
    # 第二轮对话 - 这是之前失败的地方
    print("🎯 开始第二轮对话测试...")
    
    instruction = "在这基础上分析各销售员的销售额"
    
    # 使用上下文感知的分析方法
    success, code, error_msg = integration.analyze_data_with_context(
        instruction=instruction,
        use_metadata=False
    )
    
    if not success:
        print(f"❌ 第二轮对话失败: {error_msg}")
        return False
    
    print("✅ 第二轮对话成功生成代码")
    print("生成的代码:")
    print("-" * 60)
    print(code)
    print("-" * 60)
    
    # 检查代码质量
    checks_passed = 0
    total_checks = 6
    
    # 检查1: 不包含失败标记
    if '代码生成失败' not in code:
        print("✅ 检查1通过: 代码未包含失败标记")
        checks_passed += 1
    else:
        print("❌ 检查1失败: 代码包含失败标记")
    
    # 检查2: 不包含危险操作
    if 'import os' not in code:
        print("✅ 检查2通过: 代码不包含危险导入")
        checks_passed += 1
    else:
        print("❌ 检查2失败: 代码仍包含危险导入")
    
    # 检查3: 不包含os操作
    if 'os.makedirs' not in code and 'os.path.join' not in code:
        print("✅ 检查3通过: 代码不包含os操作")
        checks_passed += 1
    else:
        print("❌ 检查3失败: 代码仍包含os操作")
    
    # 检查4: 不包含未定义变量的使用
    if 'chart_file' not in code or code.count('chart_file') <= 1:
        print("✅ 检查4通过: 代码不包含未定义变量的使用")
        checks_passed += 1
    else:
        print("❌ 检查4失败: 代码包含未定义变量的使用")
    
    # 检查5: 正确响应销售员分析请求
    if '销售员' in code:
        print("✅ 检查5通过: 代码正确响应了销售员分析请求")
        checks_passed += 1
    else:
        print("⚠️ 检查5警告: 代码可能没有正确响应销售员分析请求")
    
    # 检查6: 代码执行测试
    print("🔧 检查6: 测试代码执行...")
    exec_success, exec_error = integration.execute_code(code)
    
    if exec_success:
        print("✅ 检查6通过: 代码执行成功")
        checks_passed += 1
    else:
        print(f"❌ 检查6失败: 代码执行失败 - {exec_error}")
    
    print(f"\n📊 检查结果: {checks_passed}/{total_checks} 通过")
    
    return checks_passed >= 5  # 至少5个检查通过才算成功

if __name__ == "__main__":
    print("🔧 开始第二轮对话修复测试...")
    print("=" * 60)
    
    success = test_second_round_with_os_operations()
    
    print("=" * 60)
    if success:
        print("🎉 第二轮对话修复测试通过！")
        print("✨ os操作相关的问题已完全解决。")
    else:
        print("❌ 第二轮对话修复测试失败，仍需进一步调试。")
