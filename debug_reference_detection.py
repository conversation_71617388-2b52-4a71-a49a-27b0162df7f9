#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试引用检测机制
验证为什么"在这基础上"没有被检测到
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def debug_reference_detection():
    """调试引用检测逻辑"""
    print("🔍 调试引用检测机制")
    print("=" * 60)
    
    # 模拟引用检测逻辑
    def simulate_detect_references(instruction: str, tracker: dict) -> dict:
        """模拟引用检测过程"""
        instruction_lower = instruction.lower()
        references = {}
        
        print(f"📝 原始指令: {instruction}")
        print(f"🔤 小写转换: {instruction_lower}")
        
        # 检测时间引用和基础引用
        time_references = ['之前', '刚才', '上面', '前面', '刚刚', '刚才的', '之前的', '上面的',
                          '在这基础上', '基于', '根据', '参考', '延续', '接着', '继续']
        
        print(f"🔍 检查时间引用关键词:")
        found_time_refs = []
        for ref in time_references:
            if ref in instruction_lower:
                found_time_refs.append(ref)
                print(f"  ✅ 找到: '{ref}'")
        
        if not found_time_refs:
            print(f"  ❌ 未找到任何时间引用关键词")
            return references
        
        has_time_reference = len(found_time_refs) > 0
        print(f"📊 时间引用检测结果: {has_time_reference}")
        
        if has_time_reference:
            print(f"🔍 检查具体引用对象:")
            
            # 检测分析引用
            analysis_keywords = ['分析', '结果', 'analysis']
            found_analysis = []
            for word in analysis_keywords:
                if word in instruction_lower:
                    found_analysis.append(word)
                    print(f"  ✅ 找到分析关键词: '{word}'")
            
            if found_analysis and tracker.get('last_analysis'):
                references['analysis'] = tracker['last_analysis']
                print(f"  ✅ 添加分析引用")
            elif found_analysis:
                print(f"  ⚠️ 找到分析关键词但tracker中无last_analysis")
            else:
                print(f"  ❌ 未找到分析关键词")
            
            # 检测图表引用
            chart_keywords = ['图', '图表', 'chart', 'plot']
            found_chart = []
            for word in chart_keywords:
                if word in instruction_lower:
                    found_chart.append(word)
                    print(f"  ✅ 找到图表关键词: '{word}'")
            
            if found_chart and tracker.get('last_chart'):
                references['chart'] = tracker['last_chart']
                print(f"  ✅ 添加图表引用")
            elif found_chart:
                print(f"  ⚠️ 找到图表关键词但tracker中无last_chart")
            elif not found_chart:
                print(f"  ❌ 未找到图表关键词")
        
        print(f"📋 最终引用结果: {list(references.keys())}")
        return references
    
    # 测试用例1: 您日志中的实际指令
    print("\n🧪 测试用例1: 实际日志中的指令")
    print("-" * 40)
    
    instruction1 = "在这基础上，分析销售员的销售额"
    tracker1 = {
        'last_analysis': {
            'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
            'success': True,
            'timestamp': '2025-08-05T17:08:19'
        }
    }
    
    result1 = simulate_detect_references(instruction1, tracker1)
    
    # 测试用例2: 不同的表述方式
    print("\n🧪 测试用例2: 其他表述方式")
    print("-" * 40)
    
    test_instructions = [
        "基于刚才的分析，看看销售员表现",
        "根据上面的结果，分析销售员数据",
        "参考之前的分析，进一步看销售员",
        "延续前面的分析，研究销售员情况"
    ]
    
    for i, instruction in enumerate(test_instructions, 1):
        print(f"\n  测试 {i}: {instruction}")
        result = simulate_detect_references(instruction, tracker1)
        print(f"  结果: {list(result.keys())}")

def analyze_log_timing():
    """分析日志时间点的问题"""
    print(f"\n⏰ 分析日志时间点问题")
    print("=" * 60)
    
    print("🔍 关键时间点分析:")
    print("-" * 40)
    
    timeline = [
        {
            'time': '17:08:48',
            'event': '第二轮对话开始',
            'log': '当前指令: 在这基础上，分析销售员的销售额'
        },
        {
            'time': '17:08:48', 
            'event': '引用检测执行',
            'log': '🔗 检测到的引用: 无'
        },
        {
            'time': '我们的修复时间',
            'event': '引用关键词扩展',
            'log': '添加了"在这基础上"等关键词'
        }
    ]
    
    for item in timeline:
        print(f"⏰ {item['time']}: {item['event']}")
        print(f"   📄 {item['log']}")
    
    print(f"\n💡 关键发现:")
    print("-" * 40)
    print("1. 您的日志是在我们修复之前生成的")
    print("2. 当时的引用检测确实缺少'在这基础上'关键词")
    print("3. 所以引用检测失效是真实存在的问题")
    print("4. 现在修复后应该能正确检测")

def test_current_detection():
    """测试当前的检测能力"""
    print(f"\n🧪 测试当前修复后的检测能力")
    print("=" * 60)
    
    try:
        from core.utils.context_manager import ContextManager
        import streamlit as st
        
        # 模拟streamlit session state
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self._state = {}
                
                def __getattr__(self, key):
                    return self._state.get(key)
                
                def __setattr__(self, key, value):
                    if key.startswith('_'):
                        super().__setattr__(key, value)
                    else:
                        self._state[key] = value
                
                def __contains__(self, key):
                    return key in self._state
            
            st.session_state = MockSessionState()
        
        # 设置模拟状态
        st.session_state.context_manager = {
            'reference_tracker': {
                'last_analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'success': True,
                    'timestamp': '2025-08-05T17:08:19'
                }
            }
        }
        
        context_manager = ContextManager(enable_logging=False)
        
        # 测试您日志中的实际指令
        instruction = "在这基础上，分析销售员的销售额"
        references = context_manager._detect_references(instruction)
        
        print(f"📝 测试指令: {instruction}")
        print(f"🔍 检测结果: {list(references.keys()) if references else '无'}")
        
        if references:
            print("✅ 修复成功！现在能正确检测引用")
            for ref_type, ref_data in references.items():
                print(f"  • {ref_type}: {ref_data['code'][:50]}...")
        else:
            print("❌ 仍然无法检测引用，需要进一步调试")
        
        return len(references) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 引用检测机制调试")
    print("=" * 80)
    
    # 执行调试
    debug_reference_detection()
    analyze_log_timing()
    success = test_current_detection()
    
    print(f"\n📋 调试结论:")
    print("=" * 60)
    print("✅ 您的疑问是正确的：LLM确实收到了'在这基础上'")
    print("✅ 但问题在于：引用检测失效导致缺少引用信息")
    print("✅ 修复效果：现在应该能正确检测引用了")
    
    if success:
        print("\n🎉 修复验证成功！")
        print("💡 建议：重新测试相同的对话，应该会看到改进")
    else:
        print("\n⚠️ 需要进一步调试")
        print("💡 建议：检查实际运行环境中的代码版本")
