#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时调试引用检测问题
分析为什么修复后仍然检测不到引用
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def debug_live_reference_detection():
    """调试实时的引用检测问题"""
    print("🚨 实时引用检测问题调试")
    print("=" * 60)
    
    # 检查当前代码中的引用关键词
    try:
        with open('core/utils/context_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找引用关键词定义
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'time_references' in line and '在这基础上' in line:
                print(f"✅ 找到修复后的关键词定义 (第{i+1}行):")
                print(f"   {line.strip()}")
                # 显示接下来几行
                for j in range(1, 3):
                    if i+j < len(lines):
                        print(f"   {lines[i+j].strip()}")
                break
        else:
            print("❌ 未找到包含'在这基础上'的关键词定义")
            
            # 查找原始的关键词定义
            for i, line in enumerate(lines):
                if 'time_references' in line and '[' in line:
                    print(f"⚠️ 找到关键词定义 (第{i+1}行):")
                    print(f"   {line.strip()}")
                    # 显示接下来几行
                    for j in range(1, 3):
                        if i+j < len(lines):
                            print(f"   {lines[i+j].strip()}")
                    break
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

def test_actual_detection_logic():
    """测试实际的检测逻辑"""
    print(f"\n🧪 测试实际检测逻辑")
    print("=" * 60)
    
    try:
        from core.utils.context_manager import ContextManager
        import streamlit as st
        
        # 模拟streamlit session state
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self._state = {}
                
                def __getattr__(self, key):
                    return self._state.get(key)
                
                def __setattr__(self, key, value):
                    if key.startswith('_'):
                        super().__setattr__(key, value)
                    else:
                        self._state[key] = value
                
                def __contains__(self, key):
                    return key in self._state
            
            st.session_state = MockSessionState()
        
        # 设置模拟状态
        st.session_state.context_manager = {
            'reference_tracker': {
                'last_analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'success': True,
                    'timestamp': '2025-08-05T17:26:43'
                }
            }
        }
        
        context_manager = ContextManager(enable_logging=False)
        
        # 测试您日志中的实际指令
        test_instructions = [
            "在此基础上，分析各销售员的销售额",
            "在这基础上，分析各销售员的销售额",
            "基于刚才的分析，看看销售员表现"
        ]
        
        for instruction in test_instructions:
            print(f"\n📝 测试指令: {instruction}")
            references = context_manager._detect_references(instruction)
            
            if references:
                print(f"   ✅ 检测到引用: {list(references.keys())}")
            else:
                print(f"   ❌ 未检测到引用")
                
                # 手动调试检测过程
                instruction_lower = instruction.lower()
                print(f"   🔍 小写转换: {instruction_lower}")
                
                # 检查是否包含关键词
                keywords_to_check = ['在此基础上', '在这基础上', '基于', '刚才']
                found_keywords = []
                for keyword in keywords_to_check:
                    if keyword in instruction_lower:
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"   ✅ 找到关键词: {found_keywords}")
                    print(f"   ⚠️ 但检测逻辑可能有问题")
                else:
                    print(f"   ❌ 未找到任何关键词")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_method_signature():
    """检查方法签名和调用"""
    print(f"\n🔍 检查方法签名和调用")
    print("=" * 60)
    
    try:
        from core.utils.context_manager import ContextManager
        
        # 检查方法是否存在
        context_manager = ContextManager(enable_logging=False)
        
        if hasattr(context_manager, '_detect_references'):
            print("✅ _detect_references 方法存在")
            
            # 检查方法签名
            import inspect
            sig = inspect.signature(context_manager._detect_references)
            print(f"   方法签名: {sig}")
            
        else:
            print("❌ _detect_references 方法不存在")
        
        # 检查 build_context_for_llm 方法
        if hasattr(context_manager, 'build_context_for_llm'):
            print("✅ build_context_for_llm 方法存在")
        else:
            print("❌ build_context_for_llm 方法不存在")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def analyze_log_discrepancy():
    """分析日志差异"""
    print(f"\n📊 分析日志差异")
    print("=" * 60)
    
    print("🔍 关键对比:")
    print("-" * 40)
    
    print("我们的测试结果:")
    print("  指令: '在这基础上，分析销售员的销售额'")
    print("  结果: ✅ 检测到引用: ['analysis']")
    
    print("\n您的实际日志:")
    print("  指令: '在此基础上，分析各销售员的销售额'")
    print("  结果: ❌ 🔗 检测到的引用: 无")
    
    print("\n💡 可能的原因:")
    print("-" * 40)
    print("1. 关键词差异: '在这基础上' vs '在此基础上'")
    print("2. 代码未重新加载: 修改后的代码可能未生效")
    print("3. 缓存问题: Python模块缓存了旧版本")
    print("4. 运行环境差异: 测试环境与实际环境不同")

def suggest_immediate_fix():
    """建议立即修复方案"""
    print(f"\n🔧 立即修复建议")
    print("=" * 60)
    
    print("📋 修复步骤:")
    print("-" * 40)
    print("1. 检查关键词列表是否包含'在此基础上'")
    print("2. 重启应用确保代码重新加载")
    print("3. 清除Python缓存")
    print("4. 验证修复效果")
    
    print(f"\n🎯 需要添加的关键词:")
    print("-" * 40)
    additional_keywords = [
        '在此基础上',  # 您日志中的实际用词
        '在这个基础上',
        '此基础上',
        '这个基础上'
    ]
    
    for keyword in additional_keywords:
        print(f"  • '{keyword}'")

if __name__ == "__main__":
    print("🚨 实时引用检测问题调试")
    print("=" * 80)
    
    # 执行调试
    debug_live_reference_detection()
    success = test_actual_detection_logic()
    check_method_signature()
    analyze_log_discrepancy()
    suggest_immediate_fix()
    
    print(f"\n📋 调试结论:")
    print("=" * 60)
    print("🔍 问题确认: 引用检测确实仍然失效")
    print("💡 可能原因: 关键词不匹配或代码未重新加载")
    print("🔧 解决方案: 需要添加'在此基础上'并重启应用")
    
    if not success:
        print("\n⚠️ 需要立即修复引用检测逻辑")
    else:
        print("\n✅ 测试环境正常，可能是实际环境的问题")
